import React from 'react';
import { render, screen, fireEvent } from '../../../testUtils';
import PeopleScreen from '../People';
import { CommonActions } from '@react-navigation/native';
import PeopleDetail from '../../../components/PeopleDetail';
import fields from '../../../__mocks__/displayableProfileFields'
jest.mock("react-native-image-crop-picker", () => {
  return {
    openPicker: jest.fn(),
    openCamera: jest.fn()
  };
})

describe('PeopleScreen', () => {
  const defaultProps = {
    navigation: {
      dispatch: jest.fn(),
      navigate: jest.fn(),
      setParams: jest.fn()
    },
    route: {
      params: {
        userPerson: { type: 'admin' },
        currentFilterState: {
          selectedGroupType: "all",
          selectedOtherGroupId: null,
          selectedPersonTypes: ["people", "staffAndAdmins", "families"],
          selectedViewStyle: "list"
        }
      }
    }
  };

  it('renders PeopleList component', () => {
    render(<PeopleScreen {...defaultProps} />);
    expect(screen.getByTestId('people-list')).toBeTruthy();
  });

  it('sets navigation params on mount', () => {
    render(<PeopleScreen {...defaultProps} />);
    expect(defaultProps.navigation.dispatch).toHaveBeenCalledWith(
      CommonActions.setParams({
        showImagePicker: expect.any(Function)
      })
    );
  });
});

describe('PeopleDetail', () => {
  const defaultProps = {
    navigation: {
      dispatch: jest.fn(),
      navigate: jest.fn(),
      setParams: jest.fn()
    },
    route: {
      params: {
        personId: '123',
        userPerson: { type: 'admin' }
      }
    },
    userPerson: {
      type: 'admin',
      _id: '123'
    },
    org: {
      profileDataPrefix: () => 'profileData',
      hasCustomization: () => false
    },
    person: {
      _id: '123',
      firstName: 'John',
      lastName: 'Doe',
      type: 'person',
      displayableProfileFields: () => fields,
      getAvatarUrl: () => 'url',
      personInitials: () => 'JD',
      translatedPersonType: () => 'Student',
      profileData: {
        birthday: '2000-01-01',
        gender: 'Male',
        notesPublic: 'Test note'
      }
    },
    expanded: [0]
  };
  it('renders profile content items correctly', () => {
    const { getByText, getByTestId, getAllByRole } = render(<PeopleDetail {...defaultProps} />);
    const profileTab = getAllByRole('tab')[1];
    fireEvent(profileTab, 'onPress');
    expect(getByTestId('people.basicInfo')).toBeTruthy();
    expect(getByText('BIRTHDAY')).toBeTruthy();
    expect(getByText('GENDER')).toBeTruthy();
  });
});
