import React, { Component } from 'react';
import { StyleSheet, View, Text, TouchableWithFeedback, TouchableWithoutFeedback, TouchableOpacity, Button, Platform } from 'react-native';
import {ActionSheet} from 'native-base';
import { useTranslation } from 'react-i18next';

import PeopleList from '../../components/PeopleList';
import { imagePicker } from '../../helpers/ImagePicker';

import Nucleo from '../../components/icons/Nucleo';
import colors from '../../config/colors.json';
import * as environmentSettings from '../../shared/Settings';
import _ from 'lodash';
import moment from 'moment';

import { CommonActions } from '@react-navigation/native';

import { connect } from 'react-redux';


class PeopleScreen extends React.Component {
	static navigationOptions = ({navigation, route}) => {
		const userPerson = route.params?.userPerson ?? {type: null};  
		const showControls = _.includes(['staff', 'admin'], userPerson.type);
		
		let headerRightComponent = null;
		const activeSelectMode = route.params?.activeSelectMode ?? false;
		if (!showControls) {
			headerRightComponent = (
				<View>
					<TouchableWithoutFeedback
						onPress={() => navigation.navigate('Gallery')}
					>
						<Nucleo name="icon-img-stack" size={24} color={colors.primaryA}/>
					</TouchableWithoutFeedback>
				</View>
			)
		} else if (showControls && !activeSelectMode) {
			headerRightComponent = (
				<View style={{flexDirection: 'row'}}>
					<TouchableWithoutFeedback
						onPress={route?.params?.showImagePicker}
					>
						<Nucleo name="icon-camera" size={24} color={colors.primaryA} />
					</TouchableWithoutFeedback>
					<View style={{width: 20}}/>
					<TouchableWithoutFeedback 
						onPress={() => navigation.navigate("PeopleListSettingsModal", {onSaveFilters: route.params?.onSaveFilters, currentFilterState: route.params?.currentFilterState})}>
						<Nucleo name="icon-options" size={24} color={colors.primaryA} />
					</TouchableWithoutFeedback>
				</View>
			)
		}
		const title = route.params?.peopleListTitle ?? route.params?.t?.('navigation.people') ?? "People";
		return {
			title,
			headerLeft: (props) => {
				return 	showControls && (activeSelectMode ? (
					<Button onPress={() => route.params?.onFinishSelectMode?.()}  title={route.params?.t?.('people.done') ?? "Done"} color={colors.primaryA}/>
				) : (
					<Button onPress={() => route.params?.onEnterSelectMode?.()} title={route.params?.t?.('people.select') ?? "Select"} color={colors.primaryA}  />
				) )
			},
			headerTitleStyle: {
				alignSelf:"center",
				textAlign:"center",
			},
			headerRight: (props) => headerRightComponent
		};
	};
	
	componentDidMount() {
		this.props.navigation.dispatch(CommonActions.setParams({ showImagePicker: this._showImagePicker }));
	}
	
	_showImagePicker = () => {
		const { navigation, route } = this.props;
		const userPerson = route?.params?.userPerson ?? {};
		imagePicker(navigation, userPerson);
	}
	
	render() {
		return (
			<PeopleList navigation={this.props.navigation} route={this.props.route} subscribeToRender={this.props.subscribeToRender}/>
		)
	}
}

const mapStateToProps = (state) => ({
  subscribeToRender: state.auth.subscribeToRender,
});

export default connect(mapStateToProps)(PeopleScreen)
