import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import DeviceInfo from 'react-native-device-info';
import Meteor from 'react-native-meteor';
import { withTranslation } from 'react-i18next';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';
import {moderateScale} from '../../../shared/Scaling';
import ModalWebView from '../../../components/ModalWebView';

import styles from './styles';
import _ from 'lodash';

const localStyle = {
  buttonView: {
    marginVertical: moderateScale(10),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
    width: 30,
    height: 30,
    backgroundColor: colors.gray,
  },
};

class Campaign extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      completed: false,
      renderQuestion: true,
      renderSelections: false,
      saving: false,
      answerNumber: null,
      responseText: null,
      passedCampaign: null,
      hasCampaign: false,
      modalWebViewVisible: false,
      modalWebViewTitle: '',
      modalWebViewUrl: '',
    };
  }

  cancelFeedback() {
    this.setState({
      answerNumber: null,
      renderQuestion: true,
      renderSelections: false,
    });
  }

  submitFeedback = async () => {
    const {passedCampaign} = this.props;
    this.setState({saving: true});
    const oem = await DeviceInfo.getManufacturer();
    const type = DeviceInfo.getDeviceType();
    const id = DeviceInfo.getDeviceId();
    const osVersion = DeviceInfo.getSystemVersion();
    const uniqueId = DeviceInfo.getUniqueId();
    const data = {
      campaignId: passedCampaign._id,
      answerNumber: this.state.answerNumber,
      responseText: this.state.responseText,
    };
    data.deviceInfo = {
      oem,
      type,
      id,
      osVersion,
      uniqueId,
    };

    Meteor.call('saveCampaignResult', data, function (err, res) {
      if (err) {
        Alert.alert(
          this.props.t('dashboard.campaign.alerts.errorTitle'),
          this.props.t('dashboard.campaign.alerts.errorMessage'),
        );
      } else {
        Alert.alert(
          this.props.t('dashboard.campaign.alerts.successTitle'),
          this.props.t('dashboard.campaign.alerts.successMessage')
        );
      }
      this.setState({completed: true});
    });
    this.setState({renderSelections: false, completed: true});
  };

  toggleSelected = key => {
    const obj = this.state[key];
    const active = !obj.active;
    const color = active ? colors.primaryA : colors.grayDark;
    const icon = active ? 'icon-c-check' : 'icon-circle';
    const newState = {};
    newState[key] = {
      active,
      color,
      icon,
    };
    this.setState(newState);
  };

  onResourceCardAction = () => {
    const {passedCampaign} = this.props
    this.setState({
      modalWebViewVisible: true,
      modalWebViewTitle: passedCampaign.resourceTitle,
      modalWebViewUrl: passedCampaign.resourceLink,
    });
  };

  closeModalWebView = () => {
    this.setState({
      modalWebViewVisible: false,
    });
  };

  render() {
    const {
      renderQuestion,
      renderSelections,
      answerNumber,
      saving,
      completed,
      hasCampaign,
    } = this.state;
    const {passedCampaign} = this.props;

    const answers = [];
    for (let i = 1; i <= 10; i++) {
      answers.push(i);
    }
    let followupQuestion;
    if (answerNumber !== null && answerNumber >= 0 && answerNumber <= 6)
      followupQuestion = this.props.t('dashboard.campaign.followUpQuestions.improve');
    else if (answerNumber !== null && answerNumber >= 7 && answerNumber <= 8)
      followupQuestion = this.props.t('dashboard.campaign.followUpQuestions.delight');
    else if (answerNumber !== null && answerNumber >= 9 && answerNumber <= 10)
      followupQuestion = this.props.t('dashboard.campaign.followUpQuestions.delighted');

    return (
      <View style={styles.card}>
        {passedCampaign.campaignType && passedCampaign.campaignType === 'resource-card' ? (
          <View>
            <View style={styles.headerLabelView}>
              <Nucleo
                name="icon-survey"
                size={16}
                color={colors.primaryA}
                style={{marginRight: 8}}
              />
              <Text style={{fontWeight: 'bold', color: colors.primaryA}}>
                {this.props.t('dashboard.campaign.resourcesTitle')}
              </Text>
            </View>

            <View style={{flex: 1, flexDirection: 'column'}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    fontSize: 24,
                    color: colors.black,
                  }}>
                  {passedCampaign?.announcement}
                </Text>
                <Text
                  style={{color: colors.black, fontSize: 16, marginBottom: 24}}>
                  {passedCampaign?.resourceDescription}
                </Text>
              <TouchableOpacity
                style={styles.button}
                onPress={this.onResourceCardAction}>
                <Text style={styles.buttonText}>{this.props.t('dashboard.campaign.viewButton')}</Text>
              </TouchableOpacity>
            </View>

            <ModalWebView
              onDismiss={() => this.closeModalWebView()}
              visible={this.state.modalWebViewVisible}
              title={passedCampaign.resourceTitle}
              url={passedCampaign.resourceLink}
            />
          </View>
        ) : (
          <View>
            <View style={styles.headerLabelView}>
              <Nucleo
                name="icon-survey"
                size={16}
                color={colors.primaryA}
                style={{marginRight: 8}}
              />
              <Text style={{fontWeight: 'bold', color: colors.primaryA}}>
                {this.props.t('dashboard.campaign.surveyTitle')}
              </Text>
            </View>
            {renderQuestion && (
              <View style={{flex: 1, flexDirection: 'column'}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    fontSize: 24,
                    color: colors.black,
                  }}>
                  {passedCampaign?.announcement}
                </Text>
                <View
                  style={{
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'space-between',
                    marginTop: 8,
                    paddingHorizontal: 0,
                  }}>
                  {answers.map(answerNumber => (
                    <TouchableOpacity
                      onPress={() =>
                        this.setState({
                          answerNumber: answerNumber,
                          renderQuestion: false,
                          renderSelections: true,
                        })
                      }
                      style={localStyle.buttonView}>
                      <Text
                        style={{
                          fontWeight: 'bold',
                          fontSize: moderateScale(16),
                          color: colors.primaryA,
                        }}>
                        {answerNumber}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
            {renderSelections && (
              <View style={{flex: 1, flexDirection: 'column'}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    color: colors.primaryA,
                    marginTop: 12,
                  }}>
                  {this.props.t('dashboard.campaign.ratingResponse', { rating: answerNumber })}
                </Text>
                <Text
                  style={{
                    fontWeight: 'bold',
                    color: colors.primaryA,
                    marginTop: 12,
                  }}>
                  {followupQuestion}
                </Text>
                <TextInput
                  multiline
                  numberOfLines={4}
                  ref={input => {
                    this.inputRef = input;
                  }}
                  editable={true}
                  style={{
                    padding: 10,
                    fontSize: 18,
                    color: 'black',
                    borderRadius: 4,
                    borderColor: colors.primaryA,
                    borderWidth: 1,
                    marginVertical: 12,
                  }}
                  onChangeText={text => this.setState({responseText: text})}
                />
              </View>
            )}
            {renderSelections && !saving && (
              <TouchableOpacity
                onPress={() => this.submitFeedback()}
                style={styles.button}>
                <Text style={styles.buttonText}>{this.props.t('dashboard.campaign.submitButton')}</Text>
              </TouchableOpacity>
            )}
            {renderSelections && !saving && (
              <TouchableOpacity
                onPress={() => this.cancelFeedback()}
                style={styles.secondaryButton}>
                <Text style={styles.secondaryButtonText}>{this.props.t('dashboard.campaign.cancelButton')}</Text>
              </TouchableOpacity>
            )}
            {renderSelections && saving && (
              <View style={styles.button}>
                <ActivityIndicator
                  animating
                  size="small"
                  color={colors.white}
                />
              </View>
            )}
            {completed && (
              <View style={{flex: 1, flexDirection: 'column'}}>
                <Text
                  style={{
                    fontWeight: 'bold',
                    fontSize: 18,
                    color: colors.primaryA,
                    marginTop: 12,
                  }}>
                  {this.props.t('dashboard.campaign.thankYouMessage')}
                </Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  }
}

export default withTranslation()(Campaign);
