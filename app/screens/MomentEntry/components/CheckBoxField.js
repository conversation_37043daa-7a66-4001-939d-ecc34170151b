import React from 'react';
import { Text, View } from 'react-native';
import CheckBox from '../../../components/CheckBox';
import colors from '../../../config/colors.json';
import { styles } from './styles';

export default (props) => {
  const { field } = props;
  const requiredLabel = field.required ? (<Text style={{color: colors.darkGray}}> *required</Text>) : " ";

  return (
    <View style={{flex: 1, flexDirection:"row", marginVertical: 12}}>
      <CheckBox
        style={{paddingLeft: 0}} 
        color={colors.primaryA}
        checked={props.getMomentField(field.dataId)}
        onPress={() => { props.setMomentField(field.dataId, !props.getMomentField(field.dataId)); }}
      >
        <Text style={{ marginLeft: 20, fontSize:16, color: colors.darkGray}}>
          {field.label}
          {requiredLabel}
        </Text>
      </CheckBox>
    </View>
  )
}
