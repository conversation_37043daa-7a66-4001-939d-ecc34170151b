import React from 'react';
import { View, Text, TextInput } from 'react-native';
import colors from '../../../config/colors.json';
import {withTranslation} from "react-i18next";

const styles = {
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginVertical: 12,
  },
  label: {
    color: colors.primaryA,
    fontSize: 20,
    fontWeight: 'bold',
  },
  commentView: {
    flex: 1,
    backgroundColor: colors.lightGray,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },
};


const CommentField = (props) => (
  <View style={styles.container}>
    <Text style={styles.label}>{props.t("momentEntry.comment")}</Text>
    <View style={styles.commentView}>
      <TextInput
        testID='comment-input'
        style={{ height: 150, width: "100%", backgroundColor: colors.lightGray, color: colors.black, paddingTop: 8, paddingHorizontal: 8, paddingBottom: 8 }}
        multiline
        value={props.comment}
        onChangeText={(comment) => props.setMomentField("comment", comment)}
        placeholder=" ... "
        placeholderTextColor={colors.darkGray}
      />
    </View>
  </View>
)

export default withTranslation()(CommentField);
