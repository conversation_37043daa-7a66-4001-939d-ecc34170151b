import React from 'react';
import { View, Text, TouchableOpacity, Appearance } from 'react-native';
import moment from 'moment';
import DateTimePicker from 'react-native-modal-datetime-picker';
import Nucleo from '../../../components/icons/Nucleo';
import colors from '../../../config/colors.json';
import {withTranslation} from "react-i18next";

const styles = {
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'center',
    marginVertical: 12,
    padding: 10,
    backgroundColor: colors.lightGray,
    borderRadius: 4,
  },
  label: {
    color: colors.darkGray,
    fontSize: 20,
    fontWeight: 'bold',
  },
  timeView: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 10
  },
};


const TimeField = (props) => (
  <View style={styles.container}>
    <Text style={styles.label}>{props.t("momentEntry.time")}</Text>
    <View style={styles.timeView}>
      <TouchableOpacity testID={'time-field'} style={{flex: 1}} onPress={props.onPress}>
        <Text style={{paddingLeft: 5}}>{moment(props.time).format("h:mm a")}</Text>
      </TouchableOpacity>
      <TouchableOpacity style={{alignItems: 'center', justifyContent: 'center'}} onPress={props.onPress}>
        <Nucleo
          name="icon-small-triangle-down"
          size={24}
          color={colors.darkGray}
        />
      </TouchableOpacity>
      <DateTimePicker
        testID={'time-picker'}
        isVisible={props.isVisible}
        onConfirm={props.onConfirm}
        onCancel={props.onPress}
        mode="time"
        is24Hour={false}
        date={props.date}
        isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
        headerTextIOS="Pick a time"
      />
    </View>
  </View>
)

export default withTranslation()(TimeField);
