import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Button } from 'native-base';
import colors from '../../../config/colors.json';
import {withTranslation} from "react-i18next";

const styles = {
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginVertical: 12,
    padding: 10,
  },
};


const MediaField = (props) => (
  <View style={styles.container}>
    <TouchableOpacity
      style={{ backgroundColor: colors.darkGray, borderRadius: 4, alignItems: 'center', justifyContent: 'center', padding: 10 }}
      onPress={() => props.showPhotoHandler()}
    >
      <Text style={{color: colors.white, fontSize: 16, fontWeight: 'bold'}}>{props.t("momentEntry.addMedia")}</Text>
    </TouchableOpacity>
    <View style={{ flexDirection: "row", flex: 1, flexWrap: "wrap" }}>
      {
        props.attachedMedia?.map( (m) =>
          <View style={{width:100, height:100, padding:5, margin:5, justifyContent:"center", alignItems:"center", backgroundColor: colors.lightGray}}>
            {
              m.type.startsWith("video") ? (
                <Text style={{width:"100%", textAlign:"center", color: colors.white}}>Video (pending upload)</Text>
              ) :
              (
                <Image source={{uri: m.uri}} style={{width:100, height:100, padding: 5}}></Image>
              )
            }
          </View>
        )
      }
    </View>
  </View>
)

export default withTranslation()(MediaField);
