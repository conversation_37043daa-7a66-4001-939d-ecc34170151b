import React from 'react';
import { View, Text, TouchableOpacity, Appearance } from 'react-native';
import DateTimePicker from 'react-native-modal-datetime-picker';
import colors from '../../../config/colors.json';
import { styles } from './styles';


export default (props) => {
  const { field } = props;
  const requiredLabel = field.required ? (<Text style={{color: colors.darkGray}}> *required</Text>) : " ";

  return (
    <View style={styles.container}>
      <Text style={styles.label}>
        {field.label}
        {requiredLabel}
      </Text>
      <View style={{flex: 1, flexDirection: "row", alignItems: 'center'}}>
        <TouchableOpacity style={styles.borderedButton} onPress={()=> {props.displayTimePicker(field.dataId, true)}}>
          <Text style={{paddingLeft:5}}>
            {
              props.getMomentField(field.dataId) ?
              props.getMomentField(field.dataId) : "Choose time..."
            }
          </Text>
        </TouchableOpacity>
        <DateTimePicker
          isVisible={props.isVisible}
          onConfirm={(datetime) => { props.confirmTimePicker(field.dataId, datetime, true) }}
          onCancel={() => { props.cancelTimePicker(field.dataId) }}
          mode="time"
          is24Hour={false}
          date={props.convertToDate(props.getMomentField(field.dataId))}
          isDarkModeEnabled={Appearance.getColorScheme() === 'dark'}
        />
        { !props.getMomentField(field.dataId) &&
          <TouchableOpacity
            onPress={()=> props.setTimeFieldNow(field.dataId)}
            style={styles.borderedButton}
          >
            <Text>Now</Text>
          </TouchableOpacity>
        }
      </View>
    </View>
  )
}
