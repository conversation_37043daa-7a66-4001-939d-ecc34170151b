import React from 'react';
import {Animated} from 'react-native';

export default class FadeableItem extends React.Component {
	constructor(props) {
		super(props);
	
		this._animated = new Animated.Value(1);
	}

	componentDidMount() {
		Animated.timing(this._animated, {
		  toValue: 1,
		  duration: 300,
		}).start();
	}

	onRemove(cb) {
		Animated.timing(this._animated, {
			toValue: 0,
			duration: 300,
		  }).start( ()=> cb());
	}
	
	render() {
		const rowStyles = [
			{ opacity: this._animated },
			{
			  transform: [
				{ scale: this._animated },
				{
				  rotate: this._animated.interpolate({
					inputRange: [0, 1],
					outputRange: ['35deg', '0deg'],
					extrapolate: 'clamp',
				  })
				}
			  ],
			},
		];

		return (
			<Animated.View style={rowStyles}>
				{this.props.children}
			</Animated.View>
		)
	}
}