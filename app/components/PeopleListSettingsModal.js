import React from 'react';
import {Platform, TouchableHighlight,View} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Container, Header, Left, Right, Title, Button, Icon, Body, Text, Content, Separator, ListItem, Radio, Segment } from 'native-base';
import { useTranslation } from 'react-i18next';
import Meteor, { withTracker } from 'react-native-meteor';
import _ from 'underscore';
import SharedStyles from '../shared/SharedStyles';
import colors from '../config/colors.json';
import { CommonActions } from '@react-navigation/native';

class PeopleListSettingsModal extends React.Component {
	constructor(props) {
		super(props);

		const myGroupId = props.userPerson && (props.userPerson.checkInGroupId || props.userPerson.defaultGroupId), 
			myGroup = _.find(props.groups, (g) => { return g._id == myGroupId;}),
			myGroupName = myGroup && myGroup.name;

		const startingFilterState = props.route?.params?.currentFilterState ?? {},
			startingFilterGroup = startingFilterState.selectedOtherGroupId && _.find(props.groups, (g) => { return g._id == startingFilterState.selectedOtherGroupId }); 

		console.log("startingFilterState", startingFilterState);
		
		this.state = {
			selectedSortType: startingFilterState.selectedSortType || 'alphabetical',
			selectedPersonTypes: startingFilterState.selectedPersonTypes || ["people", "staffAndAdmins", "families"],
			selectedOtherGroupId: startingFilterGroup && startingFilterGroup._id ,
			selectedOtherGroupName: startingFilterGroup ? startingFilterGroup.name : "",
			selectedGroupType: startingFilterState.selectedGroupType || 'all',
			myGroupId,
			myGroupName,
			selectedViewStyle: startingFilterState.selectedViewStyle || 'list'
		};
	}

	componentDidMount() {
		const onSave = this.props.route?.params?.onSaveFilters ?? null;
		if (!onSave) this.props.navigation.dispatch(CommonActions.goBack());
	}

	saveFilters() {
		const fnSaveFilters= this.props.route?.params?.onSaveFilters; 
		fnSaveFilters(this.state);
		this.props.navigation.dispatch(CommonActions.goBack());
	}

	selectGroup(val)  { this.setState({selectedGroupType:val, selectedOtherGroupId: null, selectedOtherGroupName:""}); }

	selectOtherGroup() {
		this.props.navigation.navigate(
			"PeopleListSettingsDetailModal", 
			{
				onModalDismiss: (selectedItem) => { 
					this.setState({selectedOtherGroupId: selectedItem.id, selectedOtherGroupName: selectedItem.label, selectedGroupType: "other"})
				} ,
				options: this.props.groups.map( (g) => { return { label: g.name, id: g._id}}),
				title: this.props.t('people.groups')
			}
		);
	}

	selectPersonType(personType) {
		const currentPersonTypes = this.state.selectedPersonTypes || [];

		if (_.contains(currentPersonTypes, personType)) {
			this.setState({selectedPersonTypes: _.without(currentPersonTypes, personType)});
		} else {
			const joined = currentPersonTypes.concat(personType);
			this.setState({selectedPersonTypes: joined});
		}
	}

	selectSortType(sortType) { this.setState({selectedSortType: sortType}); }

	render() {

		return (
			<Container>
				<Header style={{backgroundColor: colors.white}}>
					<Left style={{flex:1}}>
						<Button transparent onPress={() => {this.props.navigation.dispatch(CommonActions.goBack());}}>
							{ Platform.OS === 'ios' ? (
							<Text style={SharedStyles.headerTitleStyle}>{this.props.t('actions.cancel')}</Text>
							) : (
							<Icon name='arrow-back-circle' style={{color: colors.primaryA}}></Icon>
							)}
						</Button>
					</Left>
					<Body style={{flex:1,justifyContent:"center",alignItems:'center'}}><Title style={SharedStyles.headerTitleStyle}>{this.props.t('people.filters')}</Title></Body>
					<Right style={{flex:1}}>
						<Button transparent onPress={this.saveFilters.bind(this)}>
							<Text style={SharedStyles.headerTitleStyle}>{this.props.t('actions.save')}</Text>
						</Button>
					</Right>
				</Header>
				<Content>
					<Separator bordered style={{height:42}}>
					<Text >{this.props.t('people.view')}</Text>
					</Separator>
					<ListItem>
						<Button small iconLeft 
							style={{backgroundColor: this.state.selectedViewStyle=="list" ? colors.primaryA : colors.white, 
									borderColor: colors.primaryA, borderRadius:4, borderWidth:0.5}} 
							onPress={()=> this.setState({selectedViewStyle:"list"})}>
							<Icon style={{color: this.state.selectedViewStyle=="list" ? colors.white : colors.primaryA}} name='list'  />
							<Text style={{color: this.state.selectedViewStyle=="list" ? colors.white : colors.primaryA}} >{this.props.t('people.list')}</Text>
						</Button>
						<Button small 
							style={{backgroundColor: this.state.selectedViewStyle=="grid" ? colors.primaryA : colors.white, 
									borderColor: colors.primaryA, borderRadius:4, borderWidth:0.5, marginLeft:5}} 
		 					iconLeft onPress={()=> this.setState({selectedViewStyle:"grid"})}>
							<Icon style={{color: this.state.selectedViewStyle=="grid" ? colors.white : colors.primaryA}} name='keypad' />
							<Text style={{color: this.state.selectedViewStyle=="grid" ? colors.white : colors.primaryA}} >{this.props.t('people.grid')}</Text>
						</Button>
					</ListItem>
					<Separator bordered style={{height:42}}>
					<Text>{this.props.t('people.group')}</Text>
					</Separator>
					<ListItem onPress={()=> this.selectGroup('all')}>
						<Left>
							<Text>{this.props.t('people.allGroups')}</Text>
						</Left>
						<Right>
							<Radio selected={this.state.selectedGroupType == 'all'} />
						</Right>
					</ListItem>
					{!_.isEmpty(this.state.myGroupId) && 
					<ListItem onPress={()=> this.selectGroup('mygroup')}>
						<Left>
							<Text>{this.props.t('people.myGroup')}: {this.state.myGroupName}</Text>
						</Left>
						<Right>
							<Radio selected={this.state.selectedGroupType == 'mygroup'} />
						</Right>
					</ListItem>
					}
					<ListItem last onPress={this.selectOtherGroup.bind(this)}>
						<Left>
							<Text>{this.props.t('people.otherGroup')}{this.state.selectedOtherGroupId && (": " + this.state.selectedOtherGroupName)}</Text>
							
							<Icon name='arrow-forward' style={{marginLeft:12}}></Icon>
						</Left>
						<Right>
							
							<Radio selected={this.state.selectedGroupType == 'other'} />
						</Right>
					</ListItem>

					<Separator bordered style={{height:42}}>
						<Text>{this.props.t('people.type')}</Text>
					</Separator>

					<ListItem onPress={()=> this.selectPersonType('people')}>
						<Left>
							<Text>{this.props.t('navigation.people')}</Text>
						</Left>
						<Right>
							<Radio selected={_.contains(this.state.selectedPersonTypes, 'people')} />
						</Right>
					</ListItem>
					<ListItem onPress={()=> this.selectPersonType('staffAndAdmins')}>
						<Left>
							<Text>{this.props.t('people.staffAndAdmins')}</Text>
						</Left>
						<Right>
							<Radio selected={_.contains(this.state.selectedPersonTypes, 'staffAndAdmins')} />
						</Right>
					</ListItem>
					<ListItem onPress={()=> this.selectPersonType('families')}>
						<Left>
							<Text>{this.props.t('people.families')}</Text>
						</Left>
						<Right>
							<Radio selected={_.contains(this.state.selectedPersonTypes, 'families')} />
						</Right>
					</ListItem>

					<Separator bordered style={{height:42}}>
						<Text>{this.props.t('people.sort')}</Text>
					</Separator>

					<ListItem onPress={()=> this.selectSortType('checkedIn')}>
						<Left>
							<Text>{this.props.t('people.checkedInFirst')}</Text>
						</Left>
						<Right>
							<Radio selected={this.state.selectedSortType == 'checkedIn'} />
						</Right>
					</ListItem>
					<ListItem onPress={()=> this.selectSortType('alphabetical')}>
						<Left>
							<Text>{this.props.t('people.alphabetical')}</Text>
						</Left>
						<Right>
							<Radio selected={this.state.selectedSortType == 'alphabetical'} />
						</Right>
					</ListItem>
				</Content>
			</Container>
		)
	}
}

const PeopleListSettingsModalWithTracker = withTracker(params => {
	const handle = Meteor.subscribe('theGroups');
	
	return {
	  groups: Meteor.collection('groups').find({}, {sort: {name: 1}}),
	  subsLoading: !handle.ready(),
	  currentUser: Meteor.user(),
	  userPerson: Meteor.user() && Meteor.collection('people').findOne({_id: Meteor.user().personId})
	};
})(PeopleListSettingsModal);

const PeopleListSettingsModalWrapper = (props) => {
	const { t } = useTranslation();
	return <PeopleListSettingsModalWithTracker {...props} t={t} />;
};

export default PeopleListSettingsModalWrapper;
