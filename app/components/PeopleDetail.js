import React from 'react';
import { View, StyleSheet, Image, Fragment, InteractionManager, Alert, TouchableHighlight, Dimensions, Platform, Appearance, Keyboard, FlatList, TouchableOpacity, Modal, TextInput, Pressable, TouchableWithoutFeedback } from 'react-native';
import { Tab, Tabs, Button, Text, Icon, ActionSheet, Accordion, StyleProvider, List, ListItem, Input, Content, CheckBox } from 'native-base';
import { useTranslation } from 'react-i18next';
import MomentList from './MomentList';
import LoadingScreen from './LoadingScreen';
import Meteor, { withTracker } from 'react-native-meteor';
import Moments from '../api/Moments';
import People from '../api/People';
import Invoices from '../api/Invoices';
import Curriculums from '../api/Curriculums';
import AvatarImage from './AvatarImage';
import SyncManager from '../shared/SyncManager';
import ImagePicker from 'react-native-image-crop-picker';
import NativePicker from 'react-native-picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import _ from '../shared/ExtendedUnderscore';
import getTheme from '../../native-base-theme/components';
import moment from 'moment';
import commonColor from '../../native-base-theme/variables/commonColor';
import Random from '../shared/Random';
import Dialog from "react-native-dialog";
import colors from '../config/colors.json';
import Nucleo from '../components/icons/Nucleo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ActivityStyles from '../screens/Activity/components/styles';
import { SelectField } from '../screens/MomentEntry/components';
import { Picker } from '@react-native-picker/picker';
import RBSheet from 'react-native-raw-bottom-sheet';
import { WebView } from 'react-native-webview'
import ModalWebView from './ModalWebView';
import * as environmentSettings from '../shared/Settings';
import Orgs from '../api/Orgs';
import { Dropdown } from 'react-native-element-dropdown';

const width = Dimensions.get('window').width;
const height = Dimensions.get('window').height;

const styles = StyleSheet.create({
	container: {
		flex:1,
		flexDirection: 'column',
		backgroundColor: colors.lighterGray,
	},
	headerContainer: {
		flexDirection: 'column',
		width: '90%',
	},
	personContainer: {
		margin: 12,
		flexDirection:"row",
		justifyContent: 'center',
	},
	profileContainer: {
		flex:1,
	},
	descriptionContainer: {
		flexShrink: 1,
		flexDirection: 'column',
		marginLeft:12
	},
	actionsContainer: {
		flexDirection: 'row',
		marginBottom:12,
		marginLeft:20
	},
	actionButtons: {
		marginRight:6,
		borderRadius:3,
		padding:5
	},
	defaultHeader: {
		flexDirection: "row",
		padding: 10,
		justifyContent: "space-between",
		alignItems: "center" ,
		backgroundColor: "#edebed"
  },
	personLabel: {
		fontSize: 24,
	},
	photo: {
		height: 80,
		width: 80,
		borderRadius: 40,
	},
	activeText: {
		color: colors.primaryA,
		fontWeight:"bold",
	},
	modal: {
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: colors.white,
		height: 52,
		width: '45%',
		borderRadius: 10,
		marginTop: 125,
		marginLeft: 185,
		flexDirection: 'row',
	},
	TextInputStyleClass: {
		height: 35,
		backgroundColor: colors.gray,
		borderRadius: 5,
		marginTop: 10,
		color: colors.black,
		paddingStart:7

	},
	MainContainer: {
		justifyContent: 'center',
		marginHorizontal: 25
	},
	text: {
		color: colors.black,
		fontSize: 14,
		fontWeight: '400',
		marginLeft: 5

	},
	dialogInput: {
		...Platform.select({
			ios: {
				height: height *.2,
			},
			android: {
				height: height *.2,
				borderBottomColor: colors.lightestGray,
				borderBottomWidth: 2 ,
				color: colors.black
			}
		}),
	  },
});

const today = new Date()
let tomorrow = new Date()
tomorrow.setDate(today.getDate() + 1)
let personId = ""
class PeopleDetail extends React.PureComponent {
	constructor(props) {
		super(props);
		
		this.thePersonSubHandle = null;
		const today = new Date();
		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);
		
		this.state = {
			isVisible: false,
			isViewReady: false,
			momentCount: 20,
			currentEditProfileField: null,
			isEditingProfileField: false,
			familyPeople: null,
			showStartDatePicker: false,
			showEndDatePicker: false,
			startDate: today,
			endDate: tomorrow,
			selectedtDateDisplay: moment(today).format('MM/DD/YYYY') + ' - ' + moment(tomorrow).format('MM/DD/YYYY'),
			choosenIndex: 0,
			modalWebViewVisible: false,
			authToken: null,
			currentDate: new Date(),
			isMainModalVisible: false,
			selectedAge:'Choose age group'
		}
	}

	componentWillUnmount() {
		InteractionManager.runAfterInteractions(async () => {
			await AsyncStorage.removeItem("peopleDetailPersonId");
			if (this.thePersonSubHandle) {
				this.thePersonSubHandle.stop();
			}
		});
	}

	componentDidMount() {

		// from: https://stackoverflow.com/questions/46127753/react-native-react-navigation-slow-transitions-when-nesting-navigators
		personId = this.props.route?.params?.personId;
		InteractionManager.runAfterInteractions(async () => {
			await AsyncStorage.setItem("peopleDetailPersonId", JSON.stringify({ personId }));
			Meteor.call("getFamilyPeople", personId, (err, result) => {
				if (result && result.familyPeople) {
					this.setState({ familyPeople: result.familyPeople })
				}
			})

		});
		this._getToken()
	}

	handleStartDateConfirm = (date) => {
		const formattedStartDate = moment(date).format('MM/DD/YYYY');
		const formattedEndDate = moment(this.state.endDate).format('MM/DD/YYYY');
		
		this.setState({
			startDate: date,
			selectedtDateDisplay: `${formattedStartDate} - ${formattedEndDate}`,
			showStartDatePicker: false
		});
	}

	handleEndDateConfirm = (date) => {
		// Convert both dates to start of day for accurate comparison
		const startOfSelectedDate = new Date(date.setHours(0, 0, 0, 0));
		const startOfStartDate = new Date(this.state.startDate.setHours(0, 0, 0, 0));
		
		if (startOfSelectedDate < startOfStartDate) {
			Alert.alert(this.props.t('alerts:errors.invalidDate'), this.props.t('alerts:errors.invalidDate'));
			return;
		}
		
		const formattedStartDate = moment(this.state.startDate).format('MM/DD/YYYY');
		const formattedEndDate = moment(date).format('MM/DD/YYYY');
		
		this.setState({
			endDate: date,
			selectedtDateDisplay: `${formattedStartDate} - ${formattedEndDate}`,
			showEndDatePicker: false
		});
	}

	handleDateCancel = () => {
		this.setState({
			showStartDatePicker: false,
			showEndDatePicker: false
		});
	};

	async _getToken() {
		try {
			const token = await AsyncStorage.getItem('reactnativemeteor_usertoken');
			//console.log("Token retrieved:", token);
			if (token !== null)
				this.setState({ "authToken": token });
		} catch (err) {
			//console.log("Error retrieiving token:", err);
			this.props.navigation.dispatch(CommonActions.goBack())
		}
	}

	openResource(item) {
		const selectedAgeGroup = this.state.selectedAge == "Choose age group" ? "" : this.state.selectedAge
		this.setState({
			modalWebViewVisible: true,
			modalWebViewUrl: environmentSettings.APP_URL + "/loginWithRedirect?dest=/people/" + personId + "/portfolio?date-range= " + this.state.selectedtDateDisplay + "&age-group=" + selectedAgeGroup + "&t=" + this.state.authToken
		});
	}

	closeModalWebView() {
		this.setState({ modalWebViewVisible: false });
	}
	dismissKeyboard() {
		Keyboard.dismiss()
	}
	onCheckIn() {
		this.props.navigation.navigate('CheckInModal', {person: this.props.person, getRatios: this.props?.route?.params?.getRatios});
	}
	onCheckOut() {
		console.log(this.state.familyPeople)
		this.props.navigation.navigate('CheckOutModal', {
			person: this.props.person,
			getRatios: this.props?.route?.params?.getRatios,
			relatedPeople: this.state.familyPeople
		});
	}
	showCheckInButtons() {
		if(!this.props.person || !this.props.userPerson) return;
		return this.props.person.type != "family" && (this.props.userPerson.type == "staff" || this.props.userPerson.type == "admin");
	}
	showMoveOptions() {
		const {groups} = this.props, personId =this.props.person && this.props.person._id;

		ActionSheet.show(
			{
				options: groups.map( (g) => { return g.name;}).concat([this.props.t('actions.cancel')]),
				cancelButtonIndex: groups.length,
				title: this.props.t('people.moveToGroup')
			},
			buttonIndex => {
				if (buttonIndex < groups.length) {
					const selectedGroup = groups[buttonIndex];
					var switchData = {
						personId,
						groupId: selectedGroup._id,
						groupName: selectedGroup.name,
						switchAllInGroup: false
					};

					Meteor.call('switchGroup', switchData);
					this?.props?.getRatios?.();
				}
			}
		);
	}

	onPressAvatar() {
		const {userPerson} = this.props;
		if (!userPerson ||
				!(userPerson.type=="admin" || userPerson.type=="staff" || userPerson._id == this.props.person._id )
		) return;

		ActionSheet.show(
			{
				options: [this.props.t('people.newFromCamera'), this.props.t('people.newFromLibrary'), this.props.t('actions.cancel')],
				cancelButtonIndex: 3,
				title: this.props.t('people.avatarOptions')
			},
			buttonIndex => {
				if (buttonIndex == 0) {
					const self = this;

					ImagePicker.openCamera({
						mediaType:"any"
					}).then( (image) => {
						console.log('Response = ', image);
						const options = {
							orgId: this.props.userPerson.orgId,
							userId: this.props.user._id,
							type: image.mime,
							uri: image.path
						};
						const avatarToken = SyncManager.addOneOff(options);

						Meteor.call('associateAvatarImage', this.props.person._id, avatarToken, (error, result) => {
							if (error) {
								Alert.alert(this.props.t('alerts:errors.avatarUploadError'), error.reason);
							} else {
								Alert.alert(this.props.t('alerts:success.avatarSuccess'));
							}
						});
					}).catch(e => {
						console.log(e);
					});

					//Meteor.call('switchGroup', switchData);
				} else if (buttonIndex == 1) {

					const self = this;

					ImagePicker.openPicker({multiple: false}).then( (image) => {
						console.log('Response = ', image);
						const options = {
							orgId: this.props.userPerson.orgId,
							userId: this.props.user._id,
							type: image.mime,
							uri: image.path
						};
						const avatarToken = SyncManager.addOneOff(options);

						Meteor.call('associateAvatarImage', this.props.person._id, avatarToken, (error, result) => {
							if (error) {
								Alert.alert(this.props.t('alerts:errors.avatarUploadError'), error.reason);
							} else {
								Alert.alert(this.props.t('alerts:success.avatarSuccess'));
							}
						})
					});
				}
			}
		);
	}

	_renderProfileContent(section) {
		return (
      <View>
        {section.items.map((item, i) => {
          if (item.label) {
            return (
              <View
                style={{
                  borderBottomColor: colors.charcoalLightest,
                  borderBottomWidth: 1,
                  flexDirection: 'row',
                }}
                key={item.key || item.label}>
                <View style={{flexDirection: 'column', flex: 1}}>
                  <Text
                    style={{fontSize: 11, padding: 5, color: colors.charcoal}}
									>
                    {item.label.toUpperCase()}
                  </Text>
                  <Text testID={`${item.label?.toLowerCase() ?? 'section'}-${i}`} style={{padding: 5}}>
                    {item.displayValue || item.value}
                  </Text>
                </View>
                {item.editable && (
                  <Button
                    onPress={() => {
                      this._editItem(item);
                    }}
                    transparent
                    style={{right: 0, flex: 0}}>
                    {/* <Icon name="create" /> */}
					<Nucleo name="icon-edit" size={20} color={colors.primaryA} style={{marginRight: 10}} />
					
                  </Button>
                )}
              </View>
            );
          }
        })}
      </View>
    );
	}

	_editItem(item) {
		const itemType = item.profileField && item.profileField.type;
		this.setState({
			currentEditProfileField: item
		});
		if (_.contains(["string", "text"], itemType)) {
			this.setState({
				isEditingProfileField: true,
				currentEditText: item.value
			});
		} else if (itemType == "select") {
			const pickerData = item.profileField.values || [];
			const currentValue = item.value || pickerData[0];
			
			NativePicker.init({
				pickerTitleText: item.label,
				pickerConfirmBtnText: this.props.t('people.confirm'),
				pickerCancelBtnText: this.props.t('actions.cancel'),
				pickerData: pickerData,
				selectedValue: [currentValue],
				onPickerConfirm: data => {
					this._saveItem(data);
				},
				onPickerCancel: () => {
					// Handle cancel if needed
				}
			});
			NativePicker.show();
		} else if (itemType == "date") {
			this.setState({ showStartDatePicker: true });
		}
	}

	_saveItem(fieldValue) {
		const fieldData = this.state.currentEditProfileField,
			personId = this.props.person._id;
		this.setState({isEditingProfileField: false});
		if (Array.isArray(fieldValue) && fieldValue.length > 0) fieldValue = fieldValue[0];
		const fieldPath = {
			fieldPath: fieldData.dataPath,
			fieldId: fieldData.profileField.name
		};
		Meteor.call("updateProfileFieldValue", personId, fieldPath, fieldValue, (error, result) => {
			if (error) {
				//setTimeout( () => { Alert.alert("Error saving field value", error.reason ) }, 1000);
			}
		});

	}

	_renderHeader(item, expanded) {
		return (
			<View style={styles.defaultHeader}>
				<Text testID={`${item.title}`} style={{ fontWeight: "600" }}>
					{" "}{item.title}
				</Text>
				{expanded
					? <Nucleo name="icon-arrow-down" size={18}  />
					: <Nucleo name="icon-arrow-up" size={18} />}
			</View>
		);
	}
	// ? <Icon style={{ fontSize: 18 }} name="ios-arrow-down" />
	// : <Icon style={{ fontSize: 18 }} name="ios-arrow-up" />}
	// ? <Nucleo name="icon-arrow-down" size={18}  />
	// 				: <Nucleo name="icon-arrow-up-3" size={18}

	_loadMoreMoments = () => {
		const newMomentCount = this.state.momentCount + 20
		this.setState({momentCount: newMomentCount});
	}

	_convertToDate(fieldValue) {

		if (typeof fieldValue == "string" || fieldValue instanceof String) {
			return new moment(fieldValue, "h:mm a").toDate();
		} else
			return new moment(fieldValue).toDate();
	}

	_toggleSubscription( channel, momentType, relationship ) {
		const personId = this.props.route?.params?.personId;
		const currentStatus = hasSubscription(channel, momentType, relationship),
		subscriptionData = {
				id: relationship._id,
				status: !currentStatus,
				momentType: momentType,
				method: channel
			};
		const currentSubs = relationship["subscriptions"] || {};
		const currentType = currentSubs[momentType] || {}
		const newSubObj = {};
		const newSubType = {}; newSubType[channel] = !currentStatus;
		newSubObj[momentType] = Object.assign({}, currentType, newSubType);
		relationship["subscriptions"] = Object.assign({}, currentSubs, newSubObj);

		let currentFamilyPeople = this.state.familyPeople;
		const newFamilyPeople = [];
		_.each(currentFamilyPeople, (fp) => {
			if (fp.relationship && fp.relationship._id == relationship._id) {
				fp.relationship = relationship;
			}
			newFamilyPeople.push(fp);
		})

		this.setState({ familyPeople: newFamilyPeople });

		Meteor.call("updateSubscription", subscriptionData);
	}

	renderOutlook = ({item}) => {
		if (!item.render) return null;

		return (
			<View style={ActivityStyles.card}>
				<View style={[ActivityStyles.headerLabelView, { marginBottom: 16 }]}>
					<Nucleo name={item.icon} size={16} color={colors.primaryA} style={{marginRight:8}}/>
					<Text style={{fontWeight: 'bold', color: colors.primaryA}}>{item.title}</Text>
				</View>
				<View style={{flex: 1}}>
					{
						item.data.map((d) => (
							<Text style={{marginBottom: 8}}>{d}</Text>
						))
					}
				</View>
			</View>
		)
	}

	render() {
		const { person, moments, groups, userPerson, org, subsLoading} = this.props;
		const currentUser = Meteor.user()

		const ageGroup = Curriculums.activitiesAgeGroups()
		const ageGroupPortfolio = [{label:'Choose age group', value: 'Choose age group'}]
		ageGroup &&	ageGroup.map((agegrouValue) => {
			ageGroupPortfolio.push({label:agegrouValue.label,value:agegrouValue.label})
		})

		const { familyPeople } = this.state;

		if (!userPerson || !person || subsLoading || !org) {
      return <LoadingScreen />;
    }

		const hasProfileData = org.profileDataPrefix() == "profileData";
		let profileDataArray = [];
		let validFamilyRelationshipIds = [];
		try {
			// safe get on userPerson function in-case render race condition from subscriptions
			const inheritedRels = userPerson?.findInheritedRelationships?.() ?? [];
			validFamilyRelationshipIds = inheritedRels.filter( r => r.relationshipType=="family").map( r => r.targetId );
		} catch (e) {
			console.log(e);
		}

		const processFieldData = (fieldArray, fieldGroup) => {
					let profileFieldsEntry = {
			title: (fieldGroup && fieldGroup.description) || this.props.t('people.basicInfo'),
			content: "",
			items: []
		};


			_.each( fieldArray, (profileField) => {
				if (profileField.type == "fieldGroup") {
					processFieldData( profileField.fields, profileField);
				} else {
					let dataValue= "";
					const dataPath =  fieldGroup ? fieldGroup.name  : "",
						dataPathFull = (dataPath != "" ? dataPath + "." : "") + profileField.name;
					if ((hasProfileData || (person.profileData && Object.keys(person.profileData).length != 0)) && _.deep(person.profileData, dataPathFull)) {
						dataValue = _.deep(person.profileData, dataPathFull);
					} else {
						dataValue = _.deep(person, dataPathFull);
					}
					let editableField = true;
					if (profileField.type=="query") {
						editableField = false;
						const qd = org.queryData(profileField.source, {personId: person._id});
						const selectedRecord = _.find(qd, (d) => { return d.id == dataValue});
						if (selectedRecord) dataValue = selectedRecord.value;
					}
					let displayValue = dataValue;
					if (profileField.type == "date") {
						displayValue = (dataValue) ? new moment(dataValue).format("M/D/YYYY") : "";
					}
					if(userPerson.type != "family" || profileField.description != "Primary Family"){
						//if item is bus route, then display the bus route name instead of the id
						if (profileField.description && profileField.description.includes("Bus") && org?.busRoutes) {
							const busRoute = org?.busRoutes.find((route) => route._id == displayValue);
							displayValue = busRoute ? busRoute.name : displayValue;
						}
						profileFieldsEntry.items.push( {
							label: profileField.description,
							value: dataValue,
							displayValue,
							key: Random.id(),
							editable: editableField && (userPerson.type=="admin" ||
									(userPerson.type=="staff" && userPerson._id == person._id) ||
									(person.type == "family" && profileField.isFamilyEditable && person._id == userPerson._id) ||
									(person.type == "person" && userPerson.type == "family" && profileField.isFamilyEditable && _.contains(validFamilyRelationshipIds, person._id)) )
									,
							dataPath,
							dataPathFull,
							profileField,
						});
					}
				}
			});
			if (fieldGroup)
				profileDataArray.push(profileFieldsEntry);
			else
				profileDataArray.unshift(profileFieldsEntry);
		};

		processFieldData( person && person.displayableProfileFields() );

		if (userPerson && person && (userPerson.type == "staff" || userPerson.type == "admin") && person.type == "person" && familyPeople) {
			const hideContactInfo = ( org.hasCustomization("people/staffViewContactInfo/disabled") && userPerson.type == "staff" );

			profileDataArray.unshift( {
				title: this.props.t('people.relationships'),
				content: "",
				items:
					familyPeople
						.filter((fp)=> { return fp.person && fp.person._id != person._id && fp.availableContactMethods})
						.sort((a,b) => a.person.firstName.localeCompare(b.person.firstName))
						.map( (fp) => {
							return {
								key: Random.id(),
								label: fp.relationshipType ? fp.relationshipType.replace("authorizedPickup", this.props.t('people.authorizedPickup')).replace("emergencyContact", this.props.t('people.emergencyContact')) : this.props.t('people.unspecifiedRelationship'),
								value: '' + (fp.person && (fp.person.firstName + ' ' + fp.person.lastName)) +
								(fp.relationshipDescription ? ' (' + fp.relationshipDescription + ')' : '') +
								(((fp?.email || fp?.profileEmailAddress || fp.person?.profileEmailAddress) && !hideContactInfo) ? "\nEmail: " + (fp.email || fp.profileEmailAddress || fp.person.profileEmailAddress) : "") +
								(hideContactInfo ? '' : fp?.availableContactMethods.map(item => {return item ? ("\n" + item.description + ": " + item.value) : ""}))
							};
						})
			});
		}else if (userPerson.type == "family"){
			//When a family user login the relationship label will be at 1st index
            profileDataArray.splice(1, 0, {
                title: this.props.t('people.relationships'),
                content: "",
                items: _.sortBy(familyPeople, (fp) => { return fp.person && (fp.person.lastName+','+fp.person.firstName); }).map( (fp) => {
					return {
                    key: Random.id(),
                    label: fp.relationshipType ? fp.relationshipType.replace("authorizedPickup", this.props.t('people.authorizedPickup')).replace("emergencyContact", this.props.t('people.emergencyContact')) : this.props.t('people.unspecifiedRelationship'),
                    value: '' + (fp.person && (fp.person.firstName + ' ' + fp.person.lastName)) +
                        (fp.relationshipDescription ? ' (' + fp.relationshipDescription + ')' : '') +
                        ((fp.email || fp.profileEmailAddress || fp.person.profileEmailAddress) ? "\nEmail: " + (fp.email || fp.profileEmailAddress || fp.person.profileEmailAddress) : "") +
                        ((fp.person) && _.map(fp.availableContactMethods, (cm) => { return "\n" + cm.description + ": " + cm.value}))
                }; })
            });
        }

		let initialTabIndex = 0;
		const showActivityTab = person.type != "family",
			showNotificationsTab = person.type == "family" && person._id == userPerson._id;

		let currentInvoices;
		let outlookData = null, hasAllergy = null, noteData = null, hasNote = null, showOutlookTab = false, hasCheckInOutlook = null,specialNeedsData = null,hasSpecialNeeds = null;
		const openToOutlook = this.props.route?.params?.openToOutlook ?? false;
		if (userPerson && (userPerson.type == "staff" || userPerson.type == "admin")) {
			outlookData = (person.profileData?.standardOutlook) ? person.profileData.standardOutlook : person.standardOutlook;
			hasAllergy = (outlookData && outlookData.allergies && outlookData.allergies.trim().length > 0) ? true : false;
			noteData = outlookData && outlookData.importantNotes;
			specialNeedsData = outlookData && outlookData.specialNeeds;
			hasNote = (noteData && noteData.trim().length > 0) ? true : false;
			hasSpecialNeeds = (specialNeedsData && specialNeedsData.trim().length > 0) ? true : false;
			hasCheckInOutlook = (person?.checkInOutlook?.length > 0) ? true : false;
			showOutlookTab = hasAllergy || hasNote || hasCheckInOutlook || hasSpecialNeeds;
			if (openToOutlook && (hasAllergy || hasNote || hasCheckInOutlook || hasSpecialNeeds) && showActivityTab) initialTabIndex = 2;
		}

		return(
			<StyleProvider style={getTheme(commonColor)}>
				<View style={styles.container}>
									<Dialog.Container 
					visible={this.state.isEditingProfileField} 
					onBackdropPress={() => this.dismissKeyboard()}
					contentStyle={{backgroundColor: colors.white}}
				>
					<Dialog.Title style={{color:colors.black}}>{this.state.currentEditProfileField && this.state.currentEditProfileField.label}</Dialog.Title>
					<Dialog.Description>{this.props.t('people.enterUpdatedValue')}</Dialog.Description>
						<Dialog.Input
							value={this.state.currentEditText}
							onChangeText={(val) => this.setState({currentEditText:val})}
							wrapperStyle={styles.dialogInput}
							style={[{flex: 1, flexWrap: 'wrap',backgroundColor: colors.lightestGray,color: colors.black}]}
							multiline={this.state.currentEditProfileField && this.state.currentEditProfileField.profileField.type == "text"}
						/>
											<Dialog.Button label={this.props.t('actions.cancel')} onPress={() => {this.setState({isEditingProfileField: false})}} />
					<Dialog.Button label={this.props.t('actions.save')} onPress={() => {this._saveItem(this.state.currentEditText)}} />
					</Dialog.Container>
					<View style={{ flexDirection: 'row', width: '100%' }}>
						<View style={styles.headerContainer}>
							<View style={styles.personContainer}>
								<TouchableHighlight onPress={() => this.onPressAvatar()} underlayColor='transparent'>
									<AvatarImage source={person.getAvatarUrl()} initials={person.personInitials()} style={styles.photo} defaultPhotoTextStyle={{ fontSize: 36 }} />
								</TouchableHighlight>
								<View style={[styles.descriptionContainer, { width: '70%' }]}>
									<View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6, }}>
										<Text style={[styles.personLabel, { flex: 0, flexWrap: 'wrap', }]}>{`${person.firstName} ${person.lastName}`}</Text>
										{hasAllergy && <Nucleo name="icon-hospital-32" size={20} color={colors.red} style={{ marginLeft: 12 }} />}
									</View>
									<View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'flex-start', }}>
										<Text>{capitalize(person.translatedPersonType())}</Text>
										{person.checkedIn && <Nucleo name="icon-check" style={{ marginLeft: 10, color: colors.greenLight, fontSize: 18 }} />}
										{person.checkInGroupName ? <Text style={{ marginLeft: 5, fontSize: 12 }}>{person.checkInGroupName}</Text> : null}
									</View>
								</View>

							</View>
							{this.showCheckInButtons() &&
								<View style={styles.actionsContainer}>
									{person.checkedIn != true ? (
										<Button testID='checkIn' small style={[styles.actionButtons, { backgroundColor: colors.primaryA }]} onPress={this.onCheckIn.bind(this)}><Text styles={styles.actionButtonText}>{this.props.t('people.checkIn')}</Text></Button>
									) : (
										<View testID='buttonContainer' style={{ flexDirection: 'row' }}>
											<Button testID='checkOut' small style={[styles.actionButtons, { backgroundColor: colors.primaryA }]} onPress={this.onCheckOut.bind(this)}><Text styles={styles.actionButtonText}>{this.props.t('people.checkOut')}</Text></Button>
											<Button small style={[styles.actionButtons, { backgroundColor: `${colors.primaryA}BF` }]} onPress={this.showMoveOptions.bind(this)}><Text styles={styles.actionButtonText}>{this.props.t('people.move')}</Text></Button>
										</View>
									)}

								</View>
							}
						</View>
						<View style={{ width: '10%', marginTop: 25 }}>
							{person.type == 'person' && org?.hasCustomization("moments/portfolio/enabled") &&
								<TouchableOpacity testID="portfolio-menu-button" onPress={() => { this.setState({ isVisible: true }) }} >
									<Nucleo name="icon-menu-5" size={30} color={colors.primaryA} style={{ transform: [{ rotate: '90deg' }] }} />
								</TouchableOpacity>}
						</View>
					</View>
					<Tabs style={styles.profileContainer}  tabBarUnderlineStyle={{backgroundColor: colors.primaryA}} initialPage={initialTabIndex}>
						{showActivityTab && <Tab heading={this.props.t('people.activity')} tabStyle={{backgroundColor: colors.grayShadeDarker}} activeTabStyle={{backgroundColor: colors.grayShadeDarker}} textStyle={{color: colors.charcoalLight}} activeTextStyle={styles.activeText} >

							<MomentList moments={moments}
								onLoadMore={this._loadMoreMoments}
								momentCount={this.state.momentCount}
								subscriptionType="personMoments"
								subscriptionDetail={person._id}
								navigation={this.props.navigation}
								route={this.props.route}
							/>

						</Tab>}
						<Tab heading={this.props.t('people.profile')} testID='Profile-tab' tabStyle={{backgroundColor: colors.grayShadeDarker}} activeTabStyle={{backgroundColor: colors.grayShadeDarker}}  textStyle={{color: colors.charcoalLight}} activeTextStyle={styles.activeText}>
							<Accordion
								dataArray={profileDataArray}
								animation={true}
								expanded={this.props.expanded ?? [0]}
								renderHeader={this._renderHeader}
								renderContent={(item) => this._renderProfileContent(item)}
								style={{marginBottom: 24}}
							/>
						</Tab>
						{showOutlookTab && <Tab heading={this.props.t('people.outlook')} tabStyle={{backgroundColor: colors.grayShadeDarker}} activeTabStyle={{backgroundColor: colors.grayShadeDarker}} textStyle={{color: colors.charcoalLight}} activeTextStyle={styles.activeText} >
							<FlatList
								style={{flex: 1, backgroundColor: `${colors.primaryA}1A`}}
								data={[{render: hasCheckInOutlook, data: person.checkInOutlook, id: "1", icon: "icon-check-in", title: this.props.t('people.checkInOutlook')}, {render: hasNote, data: [noteData], id: "2", icon: "icon-c-info", title: this.props.t('people.importantNotes')}, {render: hasAllergy, data: [outlookData?.allergies], id: "3", icon: "icon-hospital-32", title: this.props.t('people.allergies')}, {render: hasSpecialNeeds, data: [specialNeedsData], id: "4", icon: "icon-c-info", title: this.props.t('people.specialNeeds')},]}
								renderItem={this.renderOutlook}
								keyExtractor={(item) => item.id}
							/>
						</Tab>
						}
						{showNotificationsTab && <Tab heading={this.props.t('people.notifications')} tabStyle={{backgroundColor: colors.grayShadeDarker}} activeTabStyle={{backgroundColor: colors.grayShadeDarker}} textStyle={{color: colors.charcoalLight}} activeTextStyle={styles.activeText} >
							<Content><List>
								{familyPeople && _.chain(familyPeople).filter(fp => fp.relationshipType=='family' && fp.person).sortBy( fp => fp.person.lastName + "|" + fp.person.firstName).value().map( fp =>
									<View>
										<ListItem itemDivider >
											<Text>{fp.person.firstName} {fp.person.lastName}</Text>
										</ListItem>
										{_.sortBy(org.subscriptionTypes(), "label").map( st =>
										<ListItem>
											<View style={{flexDirection:"column", width:"100%"}}>
												<Text style={{alignSelf:"flex-start"}}>{st.label}</Text>
												<View style={{flexDirection:"row", alignSelf: "flex-end"}}>
													<Button iconLeft  bordered primary style={{marginRight:5}} onPress={ () => this._toggleSubscription( 'push', st.type, fp.relationship) }>
														<Icon name={hasSubscription('push', st.type, fp.relationship) ? 'checkmark-circle' : 'radio-button-off'} />
														<Text style={{ paddingLeft: 5}}>Push</Text>
													</Button>
													<Button iconLeft  bordered primary onPress={ () => this._toggleSubscription( 'email', st.type, fp.relationship) }>
														<Icon name={hasSubscription('email', st.type, fp.relationship) ? 'checkmark-circle' : 'radio-button-off'} />
														<Text style={{ paddingLeft: 5}}>Email</Text>
													</Button>
												</View>
											</View>
										</ListItem>
										)}
									</View>
								)}
							</List></Content>
						</Tab>}
					</Tabs>

					<Modal
						animationType={'fade'}
						transparent={true}
						visible={this.state.isVisible}
						style={{ padding: 0, margin: 0 }}
						onRequestClose={() => {
						this.setState({
								isVisible: false
							})
						}}>
						<TouchableWithoutFeedback   onPressOut={() => {
							this.setState({
								isVisible: false,
							})
						}}>
							<View>

								<TouchableOpacity onPress={() => {
									this.setState({
										isVisible: false,
										isMainModalVisible: true
									})

								}
								}
									style={styles.modal} activeOpacity={0.7}>
									<Nucleo name="icon-binocular" size={24} color={colors.primaryA} />
									<Text testID="portfolio-modal-text" style={styles.text}>{this.props.t('people.viewPortfolio')}</Text>
								</TouchableOpacity>
							</View>
						</TouchableWithoutFeedback>
					</Modal>

					<ModalWebView
						onDismiss={() => this.closeModalWebView()}
						visible={this.state.modalWebViewVisible}
						url={this.state.modalWebViewUrl}
					/>

					<Modal
						animationType={'fade'}
						transparent={true}
						visible={this.state.isMainModalVisible}
						onRequestClose={() => {
							this.setState({
								isMainModalVisible: false
							})
						}}
					>
						<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.5)' }}>
							<View style={{ backgroundColor: colors.white, width: '98%', borderRadius: 10, paddingBottom: 20 }}>
								<View style={{ flexDirection: 'row', justifyContent: 'space-between', marginTop: 15 }}>
									<Text testID="generate-portfolio-text" style={{
										textTransform: 'none',
										fontSize: 14,
										color: colors.black,
										marginTop: 10,
										marginLeft: 15,
										marginBottom: 10
									}}>
										{this.props.t('people.generatePortfolio')}
									</Text>
									<View style={{ marginRight: 12, marginTop: 7 }}>
										<TouchableOpacity activeOpacity={1}
											onPress={() => this.setState({ isMainModalVisible: false })}
										>
											<Nucleo name="icon-e-remove" size={20} color={colors.primaryA} />
										</TouchableOpacity>
									</View>
								</View>
								<View
									style={{
										width: '100%',
										height: 1,
										backgroundColor: '#F3F3F3',
										marginBottom: 10
									}}
								/>
								<View style={styles.MainContainer}>
									<Text
										style={{
											textTransform: 'none',
											fontSize: 14,
											color: colors.black,
										}}>
										{this.props.t('people.dateRange')}
									</Text>
									<View>
										<Pressable onPress={() => this.setState({ showStartDatePicker: true })}>
											<View pointerEvents="none">
												<TextInput
													autoCapitalize='none'
													style={styles.TextInputStyleClass}
													value={this.state.selectedtDateDisplay}
												/>
											</View>
										</Pressable>
										<View style={{flexDirection: 'row', justifyContent: 'flex-end', marginTop: 5}}>
											<TouchableOpacity 
												onPress={() => this.setState({ showStartDatePicker: true })}
												style={{marginRight: 10}}
											>
												<Text style={{color: colors.primaryA, fontSize: 12}}>{this.props.t('people.startDate')}</Text>
											</TouchableOpacity>
											<TouchableOpacity 
												onPress={() => {
													if (this.state.startDate) {
														this.setState({ showEndDatePicker: true });
													} else {
														Alert.alert(this.props.t('alerts:errors.selectStartDate'), this.props.t('alerts:errors.selectStartDate'));
													}
												}}
											>
												<Text style={{color: colors.primaryA, fontSize: 12}}>{this.props.t('people.endDate')}</Text>
											</TouchableOpacity>
										</View>
									</View>
									{this.state.showStartDatePicker && (
										<DateTimePicker
											testID="startDatePicker"
											value={this.state.startDate}
											mode="date"
											display="default"
											onChange={(event, date) => {
												this.setState({ showStartDatePicker: false });
												if (event.type === 'set' && date) {
													this.handleStartDateConfirm(date);
												}
											}}
											minimumDate={new Date()}
											accentColor={colors.primaryA}
											themeVariant="light"
										/>
									)}
									{this.state.showEndDatePicker && (
										<DateTimePicker
											testID="endDatePicker"
											value={this.state.endDate}
											mode="date"
											display="default"
											onChange={(event, date) => {
												this.setState({ showEndDatePicker: false });
												if (event.type === 'set' && date) {
													this.handleEndDateConfirm(date);
												}
											}}
											minimumDate={this.state.startDate}
											accentColor={colors.primaryA}
											themeVariant="light"
										/>
									)}
									<View style={{ marginTop: 10 }}>
										<Text
											style={{
												textTransform: 'none',
												fontSize: 14,
												color: colors.black,
											}}
										>{this.props.t('people.ageGroup')}
										</Text>

										<Dropdown
											style={[styles.TextInputStyleClass, {
												backgroundColor: colors.gray,
												borderRadius: 5,
												marginTop: 10,
											}]}
											placeholderStyle={{
												fontSize: 14,
												color: colors.black
											}}
											selectedTextStyle={{
												fontSize: 14,
												color: colors.black
											}}
											data={ageGroupPortfolio}
											maxHeight={200}
											labelField="label"
											valueField="value"
											placeholder={this.state.selectedAge}
											value={this.state.selectedAge}
											onChange={item => {
												this.setState({ selectedAge: item.value });
											}}
										/>

									</View>

								</View>


								<View
									style={{
										width: '100%',
										height: 1,
										backgroundColor: '#F3F3F3',
										marginTop: 15
									}}
								/>
								<View style={{ flexDirection: 'row', justifyContent: 'flex-end', marginRight: 15, marginTop: 20 }}>
									<TouchableOpacity style={{
										width: '20%',
										paddingVertical: 5,
										backgroundColor: colors.primaryA,
										marginEnd: 10
									}}
										onPress={() => {
											this.setState({ isMainModalVisible: false });
											this.openResource();
										}}
									>
										<Text style={{
											fontSize: 14,
											color: colors.white,
											textAlign: 'center'
										}}>
											{this.props.t('people.generate')}
										</Text>
									</TouchableOpacity>
									<TouchableOpacity style={{
										width: '20%',
										paddingVertical: 5,
										backgroundColor: colors.gray,
										marginStart: 5
									}}
										onPress={() => this.setState({ isMainModalVisible: false })}
									>
										<Text style={{
											fontSize: 14,
											color: colors.black,
											textAlign: 'center'
										}}>
											{this.props.t('actions.cancel')}
										</Text>

									</TouchableOpacity>

								</View>
							</View>
						</View>

					</Modal>

				</View>
			</StyleProvider>
		)
	}
}

function hasSubscription(channel, momentType, relationship) {
	return (relationship && relationship.subscriptions && relationship.subscriptions[momentType] && relationship.subscriptions[momentType][channel]) ? true : false;
}

const PeopleDetailWithTracker = withTracker(params => {
	const personId = params.route?.params?.personId;
	const	groupsHandle = Meteor.subscribe('theGroups'),
		orgHandle = Meteor.subscribe('theOrg'),
		invoicesHandle = Meteor.subscribe("theInvoices", { targetPersonId: personId}),
		thisPerson = People.findOne(personId),
		userPerson = Meteor.user() && Meteor.collection('people').findOne({_id: Meteor.user().personId}),
		peopleHandle = (userPerson && userPerson.type === "family") ? null : Meteor.subscribe("thePerson", {personById: personId})


	return {
		person: thisPerson ,
		subsLoading: !groupsHandle.ready() || !orgHandle.ready() || !invoicesHandle.ready() || ((userPerson && userPerson.type !== "family") && !peopleHandle.ready()) ,
		user: Meteor.user(),
	  groups: Meteor.collection('groups').find({}, {sort: {name: 1}, fields:{name:1}}),
		userPerson,
		org: Orgs.findOne({_id: Meteor.user() && Meteor.user().orgId}),
		invoices: Invoices.find({}, {sort: {createdAt: -1}} ),
	};
})(PeopleDetail);

const PeopleDetailWrapper = (props) => {
	const { t } = useTranslation();
	return <PeopleDetailWithTracker {...props} t={t} />;
};

export default PeopleDetailWrapper;

function capitalize(s) {
	if (typeof s !== 'string') return ''
  return s.charAt(0).toUpperCase() + s.slice(1)
}
