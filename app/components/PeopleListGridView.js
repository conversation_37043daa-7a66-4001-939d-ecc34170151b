import React from 'react';
import { Text, View, TouchableOpacity, TouchableHighlight, Image, Dimensions, StyleSheet } from 'react-native';
import {scale, verticalScale, moderateScale} from '../shared/Scaling';
import { SectionGrid } from 'react-native-super-grid';
import { useTranslation } from 'react-i18next';
// import { Icon } from 'native-base';
import colors from '../config/colors.json';
import Nucleo from '../components/icons/Nucleo';
import Icon from './icons/Icon';

const width = Dimensions.get('window').width;
const isCheckInAble = (person) => { return person.type=="person" || person.type=="staff" || person.type=="admin";};

const styles = StyleSheet.create({
	listNameText: {
		fontSize: moderateScale(16),
		color: colors.black,
		fontWeight: "600",
		flex: 0,
		flexWrap: 'wrap',
    marginLeft:12,
    marginTop:9
	}
});

export default (props) => {
  const { t } = useTranslation();
  const gridSections = [];
  if (props.showScheduledStatus) {
    gridSections.push({
      title: t('people.absentToday'),
      data: props.labeledPeople.filter(p => p?.familyCheckIn?.absent)
    });
    gridSections.push({
      title: t('people.scheduledToday'),
      data: props.labeledPeople.filter(p => p.isScheduledToday && !p?.familyCheckIn?.absent)
    });
    gridSections.push({
      title: t('people.notScheduledToday'),
      data: props.labeledPeople.filter(p => !p.isScheduledToday && !p?.familyCheckIn?.absent)
    });
  } else {
    gridSections.push({
      title:null,
      data: props.labeledPeople
    })
  }

  return (
    
        <SectionGrid
          keyboardShouldPersistTaps='handled'
          itemDimension={(width > 800) ? 160 : 130}
          keyExtractor={(item, index) => item._id}
          sections={gridSections}
          renderSectionHeader={({ section }) => (
            <Text style={styles.listNameText}>{section.title}</Text>
          )}
          renderItem={ ({ item }) => 
            {
              const checkedInText = item.checkedIn && item.checkInGroupName ? `- ${item.checkInGroupName}` : "";
              let bgColor = colors.charcoalLighterShade;
              if (isCheckInAble(item)) {
                if (item.checkedIn) {
                  bgColor = colors.greenLight;
                } else if (item.absent() || item.reservationCancellationToday()) {
                  bgColor = `${colors.purple}BF`;
                } else if (item.currentFamilyCheckin()) {
                  bgColor = colors.yellowLight;
                } else {
                  bgColor = colors.redLight;
                }
              }
              const outlookData = (item.profileData?.standardOutlook) ? item.profileData.standardOutlook : item.standardOutlook;
              const hasAllergy = (outlookData && outlookData.allergies && outlookData.allergies.trim().length > 0) ? true : false;
              const hasNote = (outlookData && outlookData.importantNotes && outlookData.importantNotes.trim().length > 0) ? true : false;
              const hasSpecialNeeds = (outlookData && outlookData.specialNeeds && outlookData.specialNeeds.trim().length > 0) ? true : false;
              const hasIANotes = ( item?.checkInOutlook && item?.checkInOutlook?.length > 0 ) ? true : false;
              return (
                <TouchableHighlight testID='peopleGridItem' onPress={() => props.onGridPress(item)} style={{backgroundColor: colors.white}} underlayColor={colors.grayDark}>
                  <View style={{justifyContent: 'flex-end', borderRadius: 5, padding: 10, height: (width > 800) ? 172 : 162, overflow: "hidden", backgroundColor: bgColor }}>
                    {item.getAvatarUrl() ? <Image source={{uri: item.getAvatarUrl()}} resizeMode="cover" style={{overflow:"hidden", height:100, width:"100%"}}/> : null}
                    <View style={{flexDirection: 'row', alignItems:'center'}}>
                      <Text style={{color: colors.white, fontWeight:"bold"}} numberOfLines={1}>{item.firstName} {item.lastName}</Text>
                    </View>
								    {
								    	item?.familyCheckIn?.absent ?
								    		<Text style={{color: colors.white, textTransform:'capitalize', fontSize:11}} >{item?.familyCheckIn?.absentReason}</Text>
								    		:
								    		<View style={{ flexDirection: 'row', marginTop: 2 }}>
								    			<Text style={{color: colors.white, textTransform:'capitalize', fontSize:11}}>{item.translatedPersonType()} {checkedInText}</Text>
                          {hasAllergy && <Nucleo name="icon-hospital-32" size={12} color={colors.red} style={{marginLeft:6}}/> }
                          {(hasNote || hasSpecialNeeds) && <Nucleo name="icon-c-info" size={12} color={colors.grayPlatform} style={{marginLeft:6}}/> }
                          {hasIANotes && <Nucleo name="icon-check-in" size={12} color={colors.grayPlatform} style={{marginLeft:6}}/> }
								    		</View>
								    }
                    {
                          item?.recurringDays?.length && !item?.familyCheckIn?.absent && props.showScheduledStatus ? <Text style={{color: colors.white, fontSize:11}}>{item?.recurringDays[0].scheduledDays.join()}</Text> : null
                    }
                    {props.selectMode && props.isPersonSelected(item._id) ? <Icon style={{position:"absolute", right:5, top:5, color: colors.white}} name="checkmark-circle"></Icon> : null}
                  </View>
                </TouchableHighlight>
              )
            }
          }
        />
    )
}
