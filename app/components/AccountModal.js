import React from 'react';
import { StyleSheet,View, Alert,Text, ActivityIndicator, TouchableOpacity  } from 'react-native';
import Meteor, { withTracker } from 'react-native-meteor';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Header, Left, Right, Body, Title, Button,   Container,  StyleProvider, Content, Separator, ListItem, Icon, CheckBox, ActionSheet } from 'native-base';
import SharedStyles from '../shared/SharedStyles';
import getTheme from '../../native-base-theme/components';
import commonColor from '../../native-base-theme/variables/commonColor';
import People from '../api/People';
import Invoices from '../api/Invoices';
import colors from '../config/colors.json';
import _ from 'underscore';
import moment from 'moment';
import { CommonActions } from '@react-navigation/native';
import { onPayMultiple, formatCurrency } from './utils/payment';
import Orgs from '../api/Orgs';
import Nucleo from './icons/Nucleo'

const styles = {
  columnContainer: {
    flexDirection: "column",
  },
  rowContainer: {
    flexDirection: "row"
  },
  text: {
    alignSelf: "flex-start",
  },
  alignCenter: {
	  alignSelf: "center"
  },
  centerText: {
	  textAlign: "center"
  }
};

class AccountModal extends React.Component {
	constructor(props) {
    super(props);
    this.setState = this.setState.bind(this)
    this.state = {
      showMultipleSelect: false,
      multipleInvoicesSelected: [],
      paymentProcessing: false,
      showAddMethod: false,
    };
  }

  componentDidMount() {
    const {route} = this.props;
    const pay = route?.params?.pay ?? false;
    if (pay) {
      this.setState({showMultipleSelect: true});
    }
  }

	onCloseModal = () => {
		this.props.navigation.dispatch(CommonActions.goBack())
	}

	onOpenInvoiceDetail = (invoiceId) => {
		this.props.navigation.navigate(
			"InvoiceDetailModal",
			{ invoiceId }
		);
	}

	onOpenPaymentMethod = (methodType) => {
		this.props.navigation.navigate(
			"PaymentMethodModal",
			{ methodType }
		);
	}

	creditMemoDescriptionForType(t) {
		if (this.props.currentOrg) {
			const cmType = _.find(this.props.currentOrg.availableCreditMemoTypes(), (cmt) => { return cmt.type == t;});
			return cmType ? cmType.description : t;
		}
	}

	togglePayInvoice(invoice) {
		let editableMultipleInvoicesSelected = [...this.state.multipleInvoicesSelected];

		if (editableMultipleInvoicesSelected.some( item => item._id === invoice._id)) {
      editableMultipleInvoicesSelected = editableMultipleInvoicesSelected.filter(item => item._id !== invoice._id);
    } else {
      editableMultipleInvoicesSelected.push(invoice);
    }
		this.setState({multipleInvoicesSelected: editableMultipleInvoicesSelected});
	}

	render() {
		const {invoices, userPerson, currentOrg} = this.props;

		const openInvoices = invoices.filter((i) => { return userPerson && i.amountDueForFamilyMember(userPerson._id) > 0})
		const totalOpenAmount = userPerson && _.reduce(openInvoices, (memo,i) => { return memo + i.amountDueForFamilyMember(userPerson._id);}, 0.0)
		const openCreditMemos = userPerson ? userPerson.availableCreditMemos() : [];

		const canManagePaymentMethods = currentOrg?.billing?.adyenInfo;

		return (
      <StyleProvider style={getTheme(commonColor)}>
        <Container>
          <Header style={{backgroundColor: colors.white}}>
            {canManagePaymentMethods ? (
              <Left style={{flex: 1}}>
                {this.state.showMultipleSelect ? (
                  this.state.paymentProcessing ? (
                    <View></View>
                  ) : (
                    <TouchableOpacity
                      onPress={() =>
                        this.setState({
                          showMultipleSelect: false,
                          multipleInvoicesSelected: [],
                        })
                      }>
                      <Text style={SharedStyles.headerTitleStyle}>Cancel</Text>
                    </TouchableOpacity>
                  )
                ) : (
                  <TouchableOpacity
                    onPress={() => this.setState({showMultipleSelect: true})}>
                    <Text style={SharedStyles.headerTitleStyle}>Pay...</Text>
                  </TouchableOpacity>
                )}
              </Left>
            ) : (
              <Left></Left>
            )}
            <Body
              style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              <Text
                testID="account-modal-title"
                style={SharedStyles.headerTitleStyle}>
                Your Account
              </Text>
            </Body>
            <Right style={{flex: 1}}>
              {this.state.showMultipleSelect ? (
                this.state.paymentProcessing ? (
                  <View style={SharedStyles.headerTitleStyle}>
                    <ActivityIndicator
                      color={colors.primaryA}></ActivityIndicator>
                  </View>
                ) : (
                  <TouchableOpacity
                    testID="pay-multiple-button"
                    onPress={() => {
                      onPayMultiple({
                        setState: this.setState,
                        invoices: this.state.multipleInvoicesSelected,
                        currentOrg,
                        userPerson,
                      });
                    }}>
                    <Text style={SharedStyles.headerTitleStyle}>Pay</Text>
                  </TouchableOpacity>
                )
              ) : (
                <TouchableOpacity onPress={this.onCloseModal}>
                  <Text style={SharedStyles.headerTitleStyle}>Done</Text>
                </TouchableOpacity>
              )}
            </Right>
          </Header>
          <Content>
            <Separator bordered style={{height: 42}}>
              <Text>PAYMENT METHODS</Text>
            </Separator>
            <ListItem noIndent last>
              <Body>
                {canManagePaymentMethods ? (
                  <Button
                    style={[
                      styles.alignCenter,
                      {
                        paddingHorizontal: 20,
                        backgroundColor:
                          currentOrg?.whiteLabel?.primaryColor ??
                          colors.primaryA,
                      },
                    ]}
                    onPress={() => {
                      this.onOpenPaymentMethod();
                    }}>
                    <Text
                      style={{
                        color: colors.white,
                        fontWeight: '500',
                      }}>
                      Manage Payment Methods
                    </Text>
                  </Button>
                ) : (
                  <Text style={styles.centerText}>
                    Please visit app.momentpath.com in your browser to manage
                    your payment methods
                  </Text>
                )}
              </Body>
            </ListItem>
            <Separator bordered style={{height: 42}}>
              <Text>OPEN INVOICES</Text>
            </Separator>
            {openInvoices.map((invoice, i) => (
              <ListItem
                onPress={() => {
                  this.onOpenInvoiceDetail(invoice._id);
                }}>
                <Left style={styles.rowContainer}>
                  {this.state.showMultipleSelect && (
                    <CheckBox
                      testID={`invoice-checkbox-${i}`}
                      style={{
                        marginRight: 30,
                        paddingRight: 5,
                        paddingLeft: 0,
                        paddingTop: 4,
                        width: 26,
                        height: 26,
                        alignSelf: 'center',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}
                      checked={this.state.multipleInvoicesSelected.some(
                        item => item._id === invoice._id,
                      )}
                      onPress={() => {
                        this.togglePayInvoice(invoice);
                      }}
                    />
                  )}
                  <View style={styles.columnContainer}>
                    <Text testID={`invoice-${i}-number`} style={styles.text}>
                      Invoice #{invoice.invoiceNumber}
                    </Text>
                    <Text style={styles.text} note>
                      {invoice.personName()}
                    </Text>
                  </View>
                </Left>
                <Body>
                  <Text testID={`invoice-${i}-total`} style={{marginRight: 6, textAlign: 'right'}}>
                    {formatCurrency(
                      invoice.amountDueForFamilyMember(userPerson._id),
                    )}
                  </Text>
                </Body>
                <Right>
                  {/* <Icon name="arrow-forward" /> */}
                  <Nucleo name="icon-arrow-right" />
                </Right>
              </ListItem>
            ))}
            <ListItem last>
              <Body>
                <Text>Open Invoice Total</Text>
              </Body>
              <Right>
                <Text>{formatCurrency(totalOpenAmount)}</Text>
              </Right>
            </ListItem>
            <Separator bordered style={{height: 42}}>
              <Text>CREDIT MEMOS</Text>
            </Separator>
            {openCreditMemos.map(cm => (
              <ListItem>
                <Left style={styles.columnContainer}>
                  <Text style={styles.text}>
                    {formatDate(cm.createdAt, 'M/DD/YYYY')}
                  </Text>
                  <Text style={styles.text} note>
                    {this.creditMemoDescriptionForType(cm.type)}
                    {'\n'}
                    {cm.notes}
                  </Text>
                </Left>
                <Right>
                  <Text style={{textAlign: 'right'}}>
                    {formatCurrency(cm.openAmount)}
                  </Text>
                </Right>
              </ListItem>
            ))}
            <ListItem last>
              <Body>
                <Text>Credit Memo Total</Text>
              </Body>
              <Right>
                <Text>
                  {userPerson &&
                    formatCurrency(userPerson.availableCreditMemoBalance())}
                </Text>
              </Right>
            </ListItem>
          </Content>
        </Container>
      </StyleProvider>
    );
	}
}

export default withTracker(params => {
	Meteor.subscribe("theInvoices");
	Meteor.subscribe("theOrg");
	const currentOrgs = Orgs.find({_id: Meteor.user() && Meteor.user().orgId}),
		currentOrg = currentOrgs.length > 0 && currentOrgs[0];

	return {
	  userPerson: Meteor.user() && People.findOne({_id: Meteor.user().personId}),
	  invoices: Invoices.find({openAmount:{"$gt":0}},  {sort: {createdAt: -1}}),
	  currentOrg
	};
})(AccountModal);


function formatDate(val, format) {
	if (val < ***********) val = val * 1000;
		return new moment(val).format(format);
}
