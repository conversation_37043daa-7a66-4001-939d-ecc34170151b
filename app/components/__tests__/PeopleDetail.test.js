import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import PeopleDetail from '../PeopleDetail';
import { Meteor } from 'react-native-meteor';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock react-i18next with actual translations
jest.mock('react-i18next', () => ({
  useTranslation: () => {
    // Import translation files inside the mock to avoid scope issues
    const enTranslations = require('../../localization/locales/en/translation.json');
    const enAlerts = require('../../localization/locales/en/alerts.json');
    const enBusiness = require('../../localization/locales/en/business.json');
    
    return {
      t: (key, options = {}) => {
        // Handle namespaced keys (e.g., 'alerts:errors.invalidDate')
        if (key.includes(':')) {
          const [namespace, path] = key.split(':');
          const translations = {
            alerts: enAlerts,
            business: enBusiness,
            translation: enTranslations
          };
          
          const nsTranslations = translations[namespace];
          if (nsTranslations) {
            const value = path.split('.').reduce((obj, key) => obj?.[key], nsTranslations);
            if (value && options && typeof value === 'string') {
              // Handle interpolation like {{count}}, {{message}}
              return value.replace(/\{\{(\w+)\}\}/g, (match, key) => options[key] || match);
            }
            return value || key;
          }
        }
        
        // Handle regular keys (e.g., 'people.profile')
        const value = key.split('.').reduce((obj, key) => obj?.[key], enTranslations);
        if (value && options && typeof value === 'string') {
          // Handle interpolation
          return value.replace(/\{\{(\w+)\}\}/g, (match, key) => options[key] || match);
        }
        return value || key;
      },
      i18n: { language: 'en' }
    };
  }
}));

// Mock dependencies
jest.mock('react-native-meteor', () => {
  const Meteor = {
    isClient: true,
    isServer: true,
    methods: jest.fn(),
    call: jest.fn(),
    loginWithPassword: jest.fn(),
    logout: jest.fn(),
    subscribe: jest.fn(() => ({
      ready: jest.fn(() => true),
      stop: jest.fn()
    })),
    userId: 'mockUserId',
    user: jest.fn().mockReturnValue({
      _id: 'test-user-id',
      personId: 'test-user-id',
      orgId: 'test-org-id',
      profile: {
        permissions: ['can_check_in_out', 'can_view_portfolio']
      }
    }),
    reconnect: jest.fn(),
    disconnect: jest.fn(),
    connected: true,
    callPromise: jest.fn(),
    unsubscribe: jest.fn(),
    collection: jest.fn(() => ({
      find: jest.fn(() => ({
        fetch: jest.fn(() => []),
        observe: jest.fn(),
        observeChanges: jest.fn()
      })),
      findOne: jest.fn()
    }))
  };

  return {
    __esModule: true,
    default: Meteor,
    Meteor,
    withTracker: (fn) => (Component) => Component,
    getData: jest.fn(() => ({ ddp: { sub: jest.fn() } }))
  };
});

jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn()
}));

jest.mock('react-native-image-crop-picker', () => ({
  openCamera: jest.fn(),
  openPicker: jest.fn()
}));

jest.mock('../MomentList', () => {
  return function MockMomentList() {
    return null;
  };
});

jest.mock('../../api/People', () => ({
  findOne: jest.fn(),
  find: jest.fn()
}));

jest.mock('../../api/Moments', () => ({
  find: jest.fn()
}));

jest.mock('../../api/Invoices', () => ({
  find: jest.fn()
}));

jest.mock('../../api/Orgs', () => ({
  findOne: jest.fn()
}));

jest.mock('../../shared/ExtendedUnderscore', () => {
  const originalUnderscore = jest.requireActual('underscore');
  return {
    ...originalUnderscore,
    deep: jest.fn((obj, path) => {
      if (!obj || !path) return undefined;
      const keys = path.split('.');
      let result = obj;
      for (const key of keys) {
        if (result && typeof result === 'object' && key in result) {
          result = result[key];
        } else {
          return undefined;
        }
      }
      return result;
    }),
    contains: jest.fn((array, item) => array && array.includes && array.includes(item)),
    each: originalUnderscore.each,
    find: originalUnderscore.find,
    map: originalUnderscore.map,
    chain: originalUnderscore.chain,
    sortBy: originalUnderscore.sortBy
  };
});

describe('PeopleDetail Component', () => {
  const mockProps = {
    navigation: {
      navigate: jest.fn(),
      dispatch: jest.fn()
    },
    route: {
      params: {
        personId: 'test-person-id',
        getRatios: jest.fn(),
        openToOutlook: false
      }
    },
    person: {
      _id: 'test-person-id',
      firstName: 'John',
      lastName: 'Doe',
      type: 'person',
      checkedIn: false,
      getAvatarUrl: jest.fn(),
      personInitials: jest.fn(),
      translatedPersonType: jest.fn(),
      displayableProfileFields: jest.fn(() => []),
      profileData: {}
    },
    userPerson: {
      _id: 'test-user-id',
      type: 'staff',
      orgId: 'test-org-id',
      findInheritedRelationships: jest.fn(() => [])
    },
    org: {
      profileDataPrefix: jest.fn(() => 'profileData'),
      hasCustomization: jest.fn(() => true),
      busRoutes: [],
      subscriptionTypes: jest.fn(() => [])
    },
    moments: [],
    groups: [],
    subsLoading: false,
    t: (key) => {
      const translations = {
        'people.activity': 'Activity',
        'people.profile': 'Profile',
        'people.outlook': 'Outlook',
        'people.notifications': 'Notifications',
        'people.checkIn': 'Check In',
        'people.checkOut': 'Check Out',
        'people.move': 'Move',
        'people.viewPortfolio': 'View Portfolio',
        'people.generatePortfolio': 'Generate portfolio',
        'people.dateRange': 'Date Range',
        'people.ageGroup': 'Age Group',
        'people.startDate': 'Start Date',
        'people.endDate': 'End Date',
        'people.generate': 'Generate',
        'people.relationships': 'Relationships',
        'people.allergies': 'Allergies',
        'people.importantNotes': 'Important Notes',
        'people.specialNeeds': 'Special Needs',
        'people.checkInOutlook': 'Check-In Outlook',
        'people.enterUpdatedValue': 'Enter updated value for field',
        'actions.cancel': 'Cancel',
        'actions.save': 'Save',
        'alerts:errors.invalidDate': 'End date must be greater than or equal to start date',
        'alerts:errors.selectStartDate': 'Please select a start date first',
        'alerts:errors.avatarUploadError': 'Error uploading avatar',
        'alerts:success.avatarSuccess': 'Avatar added successfully!'
      };
      return translations[key] || key;
    }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Set up default mock user with necessary permissions
    const mockUser = {
      _id: 'test-user-id',
      personId: 'test-user-id',
      orgId: 'test-org-id',
      profile: {
        permissions: ['can_check_in_out', 'can_view_portfolio']
      }
    };
    Meteor.user.mockReturnValue(mockUser);
    AsyncStorage.getItem.mockResolvedValue('test-token');
  });

  it('renders correctly', () => {
    const { getByText } = render(<PeopleDetail {...mockProps} />);
    expect(getByText('John Doe')).toBeTruthy();
  });

  it('handles portfolio modal visibility', async () => {
    const { getByTestId } = render(<PeopleDetail {...mockProps} />);

    // Find and click the portfolio menu button
    const menuButton = getByTestId('portfolio-menu-button');
    fireEvent.press(menuButton);

    // Check if modal is visible and has the correct text
    await waitFor(() => {
      expect(getByTestId('portfolio-modal-text')).toBeTruthy();
    });

    // Click the View Portfolio button to open the main modal
    const viewPortfolioButton = getByTestId('portfolio-modal-text');
    fireEvent.press(viewPortfolioButton);

    // Check if main modal is visible with generate portfolio text
    await waitFor(() => {
      expect(getByTestId('generate-portfolio-text')).toBeTruthy();
    });
  });

  describe('Org Optional Chaining Tests', () => {
    it('shows portfolio menu button when org has portfolio customization enabled', () => {
      const propsWithPortfolioEnabled = {
        ...mockProps,
        org: {
          ...mockProps.org,
          hasCustomization: jest.fn().mockImplementation((key) => key === 'moments/portfolio/enabled')
        }
      };
      const { getByTestId } = render(<PeopleDetail {...propsWithPortfolioEnabled} />);
      expect(getByTestId('portfolio-menu-button')).toBeTruthy();
    });

    it('hides portfolio menu button when org does not have portfolio customization enabled', () => {
      const propsWithPortfolioDisabled = {
        ...mockProps,
        org: {
          ...mockProps.org,
          hasCustomization: jest.fn().mockImplementation((key) => key !== 'moments/portfolio/enabled')
        }
      };
      const { queryByTestId } = render(<PeopleDetail {...propsWithPortfolioDisabled} />);
      expect(queryByTestId('portfolio-menu-button')).toBeNull();
    });

    it('displays bus route name when profile field is a bus route', async () => {
      const mockBusRoute = { _id: 'bus1', name: 'Route 1' };
      const propsWithBusRoute = {
        ...mockProps,
        org: {
          ...mockProps.org,
          busRoutes: [mockBusRoute],
          hasCustomization: jest.fn().mockImplementation((key) => key === 'moments/portfolio/enabled')
        },
        person: {
          ...mockProps.person,
          profileData: {
            busRoute: 'bus1'
          },
          displayableProfileFields: jest.fn(() => [{
            description: 'Bus Route',
            name: 'busRoute',
            type: 'string'
          }])
        },
        userPerson: {
          ...mockProps.userPerson,
          type: 'staff'
        },
        t: mockProps.t
      };
      const { getByTestId, getByText } = render(<PeopleDetail {...propsWithBusRoute} />);

      // Click on the Profile tab
      fireEvent.press(getByText('Profile'));

      // Wait for the profile content to render
      await waitFor(() => {
        expect(getByTestId('bus route-0')).toHaveTextContent('Route 1');
      });
    });
  });
});
