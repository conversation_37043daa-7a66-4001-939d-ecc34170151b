import React from 'react';
import { render, screen, fireEvent, waitFor } from '../../testUtils'
import PeopleList from '../PeopleList';
import Meteor from 'react-native-meteor';
import { Alert } from 'react-native';
import AsyncStorage from "@react-native-async-storage/async-storage";

// Mock react-i18next with actual translations
jest.mock('react-i18next', () => ({
  useTranslation: () => {
    // Import translation files inside the mock to avoid scope issues
    const enTranslations = require('../../localization/locales/en/translation.json');
    const enAlerts = require('../../localization/locales/en/alerts.json');
    const enBusiness = require('../../localization/locales/en/business.json');
    
    return {
      t: (key, options = {}) => {
        // Handle namespaced keys (e.g., 'alerts:errors.invalidDate')
        if (key.includes(':')) {
          const [namespace, path] = key.split(':');
          const translations = {
            alerts: enAlerts,
            business: enBusiness,
            translation: enTranslations
          };
          
          const nsTranslations = translations[namespace];
          if (nsTranslations) {
            const value = path.split('.').reduce((obj, key) => obj?.[key], nsTranslations);
            if (value && options && typeof value === 'string') {
              // Handle interpolation like {{count}}, {{message}}
              return value.replace(/\{\{(\w+)\}\}/g, (match, key) => options[key] || match);
            }
            return value || key;
          }
        }
        
        // Handle regular keys (e.g., 'people.profile')
        const value = key.split('.').reduce((obj, key) => obj?.[key], enTranslations);
        if (value && options && typeof value === 'string') {
          // Handle interpolation
          return value.replace(/\{\{(\w+)\}\}/g, (match, key) => options[key] || match);
        }
        return value || key;
      },
      i18n: { language: 'en' }
    };
  }
}));

jest.spyOn(Alert, 'alert');
jest.mock('react-native-meteor', () => ({
  ...jest.requireActual('react-native-meteor'),
  collection: jest.fn(() => ({
    find: jest.fn(() => [])
  })),
  user: jest.fn(),
  call: jest.fn((method, callback) => {
    if (method === 'getPeopleCountForGroup') {
      callback(null, [{
        _id: 'group1',
        counts: {
          checkedInCount: 10,
          absentCount: 2,
          remainingCount: 3,
          staffCheckedInCount: 4
        }
      }]);
    }
  })
}));
describe('PeopleList', () => {
  const mockNavigation = {
    navigate: jest.fn(),
    dispatch: jest.fn((action) => {
      if (action.type === 'setParams') {
        mockNavigation.setParams(action.payload);
      }
    }),
    setParams: jest.fn()
  };
  const defaultProps = {
    navigation: {
      navigate: jest.fn(),
      dispatch: jest.fn(),
      setParams: jest.fn()
    },
    route: {
      params: {
        currentFilterState: {
          selectedGroupType: "all",
          selectedOtherGroupId: null,
          selectedPersonTypes: ["people", "staffAndAdmins", "families"],
          selectedViewStyle: "list"
        }
      }
    },
    userPerson: { type: 'admin' },
    currentOrg: {
      hasCustomization: () => true,
      _id: 'org1'
    }
  };

  const mockFilterState = {
    selectedGroupType: "other",
    selectedOtherGroupId: "group1",
    selectedOtherGroupName: "Test Group",
    selectedPersonTypes: ["people", "staffAndAdmins", "families"],
    selectedViewStyle: "list"
  };

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders list view by default', () => {
    render(<PeopleList {...defaultProps} />);
    expect(screen.getByTestId('people-list-view')).toBeTruthy();
  });

  it('handles group switching', async () => {
    render(<PeopleList {...defaultProps} />);

    const mockGroups = [
      { _id: 'group1', name: 'Group 1' },
      { _id: 'group2', name: 'Group 2' }
    ];

    Meteor.collection = () => ({
      find: () => mockGroups
    });

    const showGroupChooser = defaultProps.navigation.setParams.mock.calls.find(
      call => call[0].showGroupChooser
    )?.[0]?.showGroupChooser;

    if (showGroupChooser) {
      showGroupChooser();
      expect(defaultProps.navigation.navigate).toHaveBeenCalledWith(
        'GroupListModal',
        expect.any(Object)
      );
    }
  });

  it('handles search functionality', async () => {
    render(<PeopleList {...defaultProps} />);

    const searchBtn = screen.getByTestId('search-btn');
    fireEvent.press(searchBtn);

    const searchInput = screen.getByTestId('search-input');
    fireEvent.changeText(searchInput, 'John');

    await waitFor(() => {
      expect(searchInput.props.value).toBe('John');
    });
  });
  it('displays group statistics for admin users', async () => {
    AsyncStorage.getItem.mockImplementation((key) => {
      if (key.includes('filterState')) {
        return Promise.resolve(JSON.stringify(mockFilterState));
      }
      return Promise.resolve(null);
    });

    const propsWithGroup = {
      ...defaultProps,
      groups: [{
        _id: 'group1',
        name: 'Test Group'
      }],
      userPerson: {
        type: "admin",
        checkInGroupId: 'group1'
      },
      route: {
        params: {
          currentFilterState: mockFilterState
        }
      }
    };

    render(<PeopleList {...propsWithGroup} />);

    await waitFor(() => {
      expect(screen.getByText('10 check-in | 2 absent | 3 remaining | 4 staff')).toBeTruthy();
    });
  });

  it('does not display group stats for non-admin users', async () => {
    const mockRatios = [{
      groupId: 'group1',
      checkinCount: 10,
      absentCount: 2,
      remainingCount: 3,
      staffCount: 4
    }];

    const nonAdminProps = {
      ...defaultProps,
      userPerson: { type: 'person' },
      route: {
        params: {
          currentFilterState: {
            selectedGroupType: "other",
            selectedOtherGroupId: "group1"
          }
        }
      }
    };

    render(<PeopleList {...nonAdminProps} />);

    await waitFor(() => {
      expect(screen.queryByText(/check-in.*absent.*remaining.*staff/)).toBeFalsy();
    });
  });
});

