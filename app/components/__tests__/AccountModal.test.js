import React from 'react';
import {
  fireEvent,
  screen,
  render,
  userEvent,
  waitFor,
} from '../../testUtils';
import Meteor from 'react-native-meteor';
import AccountModal from '../AccountModal';
import * as payUtils from '../utils/payment';
import {Alert} from 'react-native';
import {ActionSheet} from 'native-base';

jest.spyOn(payUtils, 'onPayMultiple');
jest.spyOn(Alert, 'alert');
jest.mock('native-base', () => {
  const originalModule = jest.requireActual('native-base');
  return {
    ...originalModule,
    ActionSheet: {
      ...originalModule.ActionSheet,
      show: jest.fn(),
    },
  };
});
describe('AccountModal Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders with billing enabled', () => {
    const props = {
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 123,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
        },
      },
    };
    render(<AccountModal {...props} />);
    const {queryByText, getByTestId} = screen;
    const header = queryByText('Your Account');
    const managePaymentMethods = queryByText('Manage Payment Methods');
    expect(header).toBeOnTheScreen();
    expect(managePaymentMethods).toBeOnTheScreen();
  });

  it('renders with billing disabled', () => {
    const props = {
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
      },
      currentOrg: {
        hasCustomization: jest.fn(() => false)
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 123,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
      ],
    };
    render(<AccountModal {...props} />);
    const {queryByText, getByTestId} = screen;
    const header = queryByText('Your Account');
    const managePaymentMethods = queryByText('Manage Payment Methods');
    const message = queryByText(
      'Please visit app.momentpath.com in your browser to manage your payment methods',
    );
    expect(header).toBeOnTheScreen();
    expect(managePaymentMethods).toBeNull();
    expect(message).toBeOnTheScreen();
  });

  it('opens detail modal', async () => {
    const props = {
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
      ],
    };
    render(<AccountModal {...props} />);
    const {queryByText} = screen;
    const user = userEvent.setup();
    const invoice = queryByText('Invoice #1');
    await user.press(invoice);
    expect(props.navigation.navigate).toHaveBeenCalledWith(
      'InvoiceDetailModal',
      {invoiceId: 'invoice1'},
    );
  });

  it('adds invoice to payment selection', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
    };
    render(<AccountModal {...props} />);
    const { queryByTestId } = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    await waitFor(() => expect(payUtils.onPayMultiple).toHaveBeenCalled())
    await waitFor(() => expect(Alert.alert).not.toHaveBeenCalledWith(
      'You must select at least one invoice to pay.',
    ));
  });

  it('shows error when no invoices are selected', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
    };
    render(<AccountModal {...props} />);
    const {queryByTestId, queryByText} = screen;
    const user = userEvent.setup();
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(payButton);
    expect(Alert.alert).toHaveBeenCalledWith(
      'You must select at least one invoice to pay.',
    );
  });

  it('shows correct fee in ACH payment confirmation', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
          passthroughFees: true
        },
        achFeeNotice: jest.fn(() => '$2'),
        cardFeeNotice: jest.fn(() => '$5'),
        getAlternateServiceChargeFeeDescription: jest.fn(() => null),
      },
    };
    const mockOptions = [
      {
        desc: 'Bank Account (1234)',
        type: 'bank_account',
      },
      {
        desc: 'Credit Card (5678)',
        type: 'card',
      },
    ];
    render(<AccountModal {...props} />);
    const { queryByTestId } = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    expect(ActionSheet.show).toHaveBeenCalledWith(
      {
        options: mockOptions.map(o => o.desc).concat('Cancel'),
        cancelButtonIndex: mockOptions.length,
        title: 'Select Payment Method',
      },
      expect.any(Function),
    );
    const actionSheetCallback = ActionSheet.show.mock.calls[0][1];
    actionSheetCallback(0); // Simulate selecting mockOption #1
    expect(Alert.alert).toHaveBeenCalledWith(
      'Confirm Payment',
      'Your bank account will be charged $100.00 plus a platform fee of $2',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: expect.any(Function),
        },
      ],
      {cancelable: false},
    );
  });

  it('shows correct fee in ACH payment confirmation with partial passthroughFeeAccount array', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
          passthroughFeesAccountTypes: ['card'],
        },
        achFeeNotice: jest.fn(() => '$2'),
        cardFeeNotice: jest.fn(() => '$5'),
        getAlternateServiceChargeFeeDescription: jest.fn(() => null),
      },
    };
    const mockOptions = [
      {
        desc: 'Bank Account (1234)',
        type: 'bank_account',
      },
      {
        desc: 'Credit Card (5678)',
        type: 'card',
      },
    ];
    render(<AccountModal {...props} />);
    const { queryByTestId } = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    expect(ActionSheet.show).toHaveBeenCalledWith(
      {
        options: mockOptions.map(o => o.desc).concat('Cancel'),
        cancelButtonIndex: mockOptions.length,
        title: 'Select Payment Method',
      },
      expect.any(Function),
    );
    const actionSheetCallback = ActionSheet.show.mock.calls[0][1];
    actionSheetCallback(0); // Simulate selecting mockOption #1
    expect(Alert.alert).toHaveBeenCalledWith(
      'Confirm Payment',
      'Your bank account will be charged $100.00',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: expect.any(Function),
        },
      ],
      {cancelable: false},
    );
  });

  it('shows correct fee in card payment confirmation', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
          passthroughFees:true
        },
        achFeeNotice: jest.fn(() => '$2'),
        cardFeeNotice: jest.fn(() => '$5'),
        getAlternateServiceChargeFeeDescription: jest.fn(() => null),
      },
    };
    const mockOptions = [
      {
        desc: 'Bank Account (1234)',
        type: 'bank_account',
      },
      {
        desc: 'Credit Card (5678)',
        type: 'card',
      },
    ];
    render(<AccountModal {...props} />);
    const {queryByTestId} = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    expect(ActionSheet.show).toHaveBeenCalledWith(
      {
        options: mockOptions.map(o => o.desc).concat('Cancel'),
        cancelButtonIndex: mockOptions.length,
        title: 'Select Payment Method',
      },
      expect.any(Function),
    );
    const actionSheetCallback = ActionSheet.show.mock.calls[0][1];
    actionSheetCallback(1); // Simulate selecting mockOption #2
    expect(Alert.alert).toHaveBeenCalledWith(
      'Confirm Payment',
      'Your credit card will be charged $100.00 plus a platform fee of $5',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: expect.any(Function),
        },
      ],
      {cancelable: false},
    );
  });

  it('shows correct fee in card payment confirmation with partial passthroughFeeAccount array', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
          passthroughFees:true,
          passthroughFeesAccountTypes: ['bank_account']
        },
        achFeeNotice: jest.fn(() => '$2'),
        cardFeeNotice: jest.fn(() => '$5'),
        getAlternateServiceChargeFeeDescription: jest.fn(() => null),
      },
    };
    const mockOptions = [
      {
        desc: 'Bank Account (1234)',
        type: 'bank_account',
      },
      {
        desc: 'Credit Card (5678)',
        type: 'card',
      },
    ];
    render(<AccountModal {...props} />);
    const {queryByTestId} = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    expect(ActionSheet.show).toHaveBeenCalledWith(
      {
        options: mockOptions.map(o => o.desc).concat('Cancel'),
        cancelButtonIndex: mockOptions.length,
        title: 'Select Payment Method',
      },
      expect.any(Function),
    );
    const actionSheetCallback = ActionSheet.show.mock.calls[0][1];
    actionSheetCallback(1); // Simulate selecting mockOption #2
    expect(Alert.alert).toHaveBeenCalledWith(
      'Confirm Payment',
      'Your credit card will be charged $100.00',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: expect.any(Function),
        },
      ],
      {cancelable: false},
    );
  });

  it('shows alt fee message', async () => {
    const props = {
      route: {
        params: {
          pay: true,
        },
      },
      navigation: {
        dispatch: jest.fn(),
        navigate: jest.fn(),
      },
      userPerson: {
        _id: 'user1',
        availableCreditMemos: jest.fn(() => []),
        availableCreditMemoBalance: jest.fn(() => 0),
        connectedBankAccount: jest.fn(() => ({last4: '1234'})),
        connectedCreditCard: jest.fn(() => ({last4: '5678'})),
      },
      invoices: [
        {
          _id: 'invoice1',
          invoiceNumber: 1,
          personName: () => 'John Doe',
          amountDueForFamilyMember: () => 100,
        },
        {
          _id: 'invoice2',
          invoiceNumber: 2,
          personName: () => 'Jane Doe',
          amountDueForFamilyMember: () => 200,
        },
      ],
      currentOrg: {
        hasCustomization: jest.fn(() => true),
        billing: {
          adyenInfo: {},
        },
        achFeeNotice: jest.fn(() => '$2'),
        cardFeeNotice: jest.fn(() => '$5'),
        getAlternateServiceChargeFeeDescription: jest.fn(
          () => 'and this alt fee',
        ),
      },
    };
    const mockOptions = [
      {
        desc: 'Bank Account (1234)',
        type: 'bank_account',
      },
      {
        desc: 'Credit Card (5678)',
        type: 'card',
      },
    ];
    render(<AccountModal {...props} />);
    const {queryByTestId} = screen;
    const user = userEvent.setup();
    const checkbox = queryByTestId('invoice-checkbox-0')
    const payButton = queryByTestId('pay-multiple-button');
    await user.press(checkbox);
    await user.press(payButton);
    expect(ActionSheet.show).toHaveBeenCalledWith(
      {
        options: mockOptions.map(o => o.desc).concat('Cancel'),
        cancelButtonIndex: mockOptions.length,
        title: 'Select Payment Method',
      },
      expect.any(Function),
    );
    const actionSheetCallback = ActionSheet.show.mock.calls[0][1];
    actionSheetCallback(1); // Simulate selecting mockOption #2
    expect(Alert.alert).toHaveBeenCalledWith(
      'Confirm Payment',
      'Your credit card will be charged $100.00. and this alt fee',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'OK',
          onPress: expect.any(Function),
        },
      ],
      {cancelable: false},
    );
  });
});
