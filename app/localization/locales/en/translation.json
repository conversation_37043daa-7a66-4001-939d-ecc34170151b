{"navigation": {"dashboard": "Dashboard", "people": "People", "activity": "Activity", "gallery": "Gallery", "moments": "Moments", "settings": "Settings"}, "actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "back": "Back", "next": "Next", "done": "Done", "close": "Close", "confirm": "Confirm", "yes": "Yes", "no": "No"}, "authentication": {"signIn": "Sign In", "signOut": "Sign Out", "username": "Username", "password": "Password", "pinCode": "PIN Code", "supplementalPinCode": "Supplemental PIN Code", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "enterCode": "Enter Code", "emailAddress": "Email Address", "baseUrl": "Base URL", "passwordHelp": "Password help", "verifyPin": "<PERSON><PERSON><PERSON>", "ssoLogin": "SSO Login", "saveAndConnect": "Save and Connect", "getConnectionStatus": "Get Connection Status", "useUsernamePassword": "Use Username and Password", "usePinCode": "Already logged in? Use your PIN code", "enterUsernameFirst": "Please enter your username first.", "forgotPasswordError": "Error with <PERSON><PERSON> Password", "checkEmailVerification": "Please check your email for a verification code", "mustEnterUsername": "You must enter a username", "invalidCredentials": "Invalid username or password", "networkError": "Network error. Please try again.", "loginError": "<PERSON><PERSON> failed. Please try again."}, "people": {"checkIn": "Check In", "checkOut": "Check Out", "move": "Move", "moveToGroup": "Move to...", "selectGroup": "Choose group", "absentToday": "Absent Today", "scheduledToday": "Scheduled Today", "notScheduledToday": "Not Scheduled Today", "completedBy": "Completed By", "chooseAction": "Choose Action", "sleepCheck": "Sleep Check", "nameToFace": "Name to Face", "search": "Search", "allGroups": "All Groups", "people": "People", "select": "Select", "done": "Done", "profile": "Profile", "activity": "Activity", "outlook": "Outlook", "notifications": "Notifications", "relationships": "Relationships", "basicInfo": "Basic Info", "allergies": "Allergies", "importantNotes": "Important Notes", "specialNeeds": "Special Needs", "checkInOutlook": "Check-In Outlook", "generatePortfolio": "Generate portfolio", "viewPortfolio": "View Portfolio", "dateRange": "Date Range", "ageGroup": "Age Group", "chooseAgeGroup": "Choose age group", "startDate": "Start Date", "endDate": "End Date", "generate": "Generate", "noActiveSchedule": "No active schedule", "filters": "Filters", "view": "VIEW", "list": "List", "grid": "Grid", "group": "GROUP", "type": "TYPE", "families": "Families", "staffAndAdmins": "Staff & Admins", "sort": "SORT", "checkedInFirst": "Checked In First", "alphabetical": "Alphabetical (Last name first)", "myGroup": "My group", "otherGroup": "Other group", "bathroom": "Bathroom", "split": "Split", "extracurricular": "Extracurricular", "other": "Other", "writeReason": "Write the reason", "chooseOneOption": "Is necessary to choose one option.", "dontSave": "Don't save", "chooseValue": "Choose a value", "rosterChanged": "This roster has changed-perform a Name to Face", "noMatchingResults": "No matching results for your search", "noResultsWithFilters": "No results match your search text and filters", "searchAll": "Search All", "tagInMoment": "Tag In Moment", "errorChecking": "Error: {{message}}", "successCheckedIn": "Success: Checked in {{count}}", "successCheckedOut": "Success: Checked out {{count}}", "mustSelectPerson": "You must select at least one person for sleep check.", "successfulSleepChecks": "Successful sleep checks: {{count}}", "sleepNotFound": "Sleep not found: {{count}}", "nameToFaceIssues": "Name-to-Face Issue(s)", "couldNotRetrieveData": "Could not retrieve Name to Face data", "avatarOptions": "Avatar options", "newFromCamera": "New from camera...", "newFromLibrary": "New from library...", "confirm": "Confirm", "unspecifiedRelationship": "Unspecified Relationship", "authorizedPickup": "authorized pickup", "emergencyContact": "Emergency Contact", "groups": "Groups", "enterUpdatedValue": "Enter updated value for field"}, "forms": {"title": "Title", "notes": "Notes", "reason": "Reason", "amount": "Amount", "type": "Type", "date": "Date", "time": "Time", "description": "Description"}, "status": {"checkedIn": "Checked In", "checkedOut": "Checked Out", "absent": "Absent", "present": "Present", "scheduled": "Scheduled", "notScheduled": "Not Scheduled"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "retry": "Retry", "refresh": "Refresh"}, "settings": {"selectLanguage": "Select Language", "language": "Language", "changeLanguage": "Change Language", "english": "English", "spanish": "Spanish"}, "dashboard": {"headerTitle": "Dashboard", "upNext": "Up-Next", "arrivingSoon": "{{count}} Arriving Soon", "leavingSoon": "{{count}} Leaving Soon", "here": "{{count}} Here", "outlook": "Outlook", "announcements": "Announcements", "greeting": "Hey {{firstName}}!", "checkedIntoGroup": "You are checked into {{groupName}}", "checkedIn": "You are checked in", "notCheckedIn": "You are not checked in", "staffImpact": {"title": "Your Impact Today", "defaultMessage": "Be great today! Check here for an update on your progress."}, "lightbridgeResources": {"title": "Important Resources", "description": "Here are links to important resources:", "tortal": "<PERSON><PERSON>", "tortalDescription": "Lightbridge Training", "intranet": "Intranet", "intranetDescription": "Lightbridge News and Documentation"}, "lightbridgePeek": {"title": "Peek of the Week", "header": "Peek of the Week", "noDataMessage": "No peeks available for today. Stay tuned!"}, "survey": {"title": "How are we doing?", "question": "We made some changes to the home page! Are you enjoying those changes?", "happyFollowUp": "Great! Which changes do you like:", "sadFollowUp": "What needs improvement?:", "upNextSection": "Up-Next section", "outlookSection": "Outlook section", "expressDriveUpSection": "Express Drive-Up", "announcementsSection": "Announcements", "anythingElse": "Anything Else?", "submitFeedback": "Submit <PERSON>"}, "sleep": {"title": "Who's <PERSON><PERSON><PERSON>"}, "timeCard": {"title": "Time Card Approval", "reviewTitle": "Review Time Cards", "period": "Period", "timeReported": "Time Reported", "changePeriod": "Change Period", "approveSelected": "Approve Selected", "pendingApproval": "You've got time to approve", "review": "Review"}, "dataValidation": {"title": "Is your information up-to-date?", "verifyButton": "Verify Information"}, "tray": {"chatSupport": "Chat with the school", "whatsNew": "What's New", "learningPath": "LearningPath"}, "campaign": {"resourcesTitle": "Resources and Information", "surveyTitle": "How are we doing?", "viewButton": "View", "submitButton": "Submit <PERSON>", "cancelButton": "Cancel", "ratingResponse": "You gave us a '{{rating}}'...", "thankYouMessage": "Thanks so much for your feedback.", "followUpQuestions": {"improve": "How can we get it right next time?", "delight": "How can we delight you next time?", "delighted": "How did we delight you?"}, "alerts": {"errorTitle": "Error", "errorMessage": "We experienced an error saving your response, please try again", "successTitle": "Thanks!", "successMessage": "Your feedback submitted successfully"}}}, "momentTypes": {"comment": "Comment", "food": "Food", "potty": "<PERSON><PERSON>", "sleep": "Sleep", "activity": "Activity", "medical": "Medical", "learning": "Learning", "mood": "<PERSON><PERSON>", "checkIn": "Check In", "checkOut": "Check Out", "move": "Move", "incident": "Incident", "alert": "<PERSON><PERSON>", "supplies": "Supplies", "illness": "Illness", "ouch": "Ouch", "bkHealthAlert": "Health Alert"}, "momentEntry": {"moment": "Moment", "attribution": "Attribution", "tag": "Tag", "comment": "Comment", "date": "Date", "time": "Time", "dateTime": "Date and Time", "save": "Save", "close": "Close", "addMedia": "Add Media", "overrideTitle": "New Check-In", "timeCardWarning": "Log into the web app to edit time cards."}}