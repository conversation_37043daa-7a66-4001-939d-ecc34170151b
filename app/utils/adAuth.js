import AsyncStorage from '@react-native-async-storage/async-storage';

import constants from '../config/constants.json';


export const COGNITO_AUTHORIZE_URL = constants.CHAMPIONS_SSO_URL;
export const CLIENT_ID = constants.CHAMPIONS_SSO_CLIENT_ID;
export const REDIRECT_URI = constants.CHAMPIONS_SSO_REDIRECT_URI;
export const SCOPE = constants.CHAMPIONS_SSO_SCOPE || 'openid email profile'; // Default scopes

export const SSO_ENABLED = constants.CHAMPIONS_SSO_ENABLED || false;
export const INCOGNITO_MODE = constants.SSO_INCOGNITO_MODE || false;

const AUTH_STATE_KEY = '@champions_auth_state';
// The path of your redirect URI, used to identify when the webview navigates to the backend handler.
const EXPECTED_REDIRECT_PATH = '/adssoauth';

export const generateCredentialToken = () => {
  return Math.random().toString(36).substring(2);
};

export const buildAuthUrl = (state) => {
  if (!COGNITO_AUTHORIZE_URL || !CLIENT_ID || !REDIRECT_URI) {
    console.error("SSO configuration is missing. Check constants.json for CHAMPIONS_SSO_URL, CHAMPIONS_SSO_CLIENT_ID, CHAMPIONS_SSO_REDIRECT_URI.");
    return null;
  }
  const params = new URLSearchParams({
    response_type: 'code',
    client_id: CLIENT_ID,
    redirect_uri: REDIRECT_URI,
    scope: SCOPE,
    state: state,
    // To use a specific IdP directly (like Azure AD federated via Cognito):
    // identity_provider: 'Azure-AD-SSO' // Ensure this matches your Cognito IdP name
  });
  return `${COGNITO_AUTHORIZE_URL}?${params.toString()}`;
};

export const storeAuthState = async (stateData) => {
  try {
    await AsyncStorage.setItem(AUTH_STATE_KEY, JSON.stringify(stateData));
  } catch (error) {
    console.error('Error storing auth state:', error);
  }
};

export const getAuthState = async () => {
  try {
    const stateJSON = await AsyncStorage.getItem(AUTH_STATE_KEY);
    return stateJSON ? JSON.parse(stateJSON) : null;
  } catch (error) {
    console.error('Error getting auth state:', error);
    return null;
  }
};

export const clearAuthData = async () => {
  try {
    await AsyncStorage.removeItem(AUTH_STATE_KEY);
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

export const parseUrlParams = (url) => {
  try {
    const search = url.substring(url.indexOf("?") + 1);
    const params = new URLSearchParams(search);
    return {
      code: params.get('code'),
      state: params.get('state'),
      error: params.get('error'),
      error_description: params.get('error_description')
    };
  } catch (error) {
    console.error('Error parsing URL params:', error, 'URL:', url);
    return {};
  }
};

// Function to check if the URL is the backend redirect that contains the code
export const isBackendRedirect = (url) => {
  try {
    const parsedUrl = new URL(url); // Using URL constructor for robust parsing
    // Check if the hostname and pathname match your redirect URI.
    // This is more robust than `url.includes(EXPECTED_REDIRECT_PATH)` if REDIRECT_URI is a full URL.
    const redirectUriInstance = new URL(REDIRECT_URI);
    return parsedUrl.origin === redirectUriInstance.origin && parsedUrl.pathname === redirectUriInstance.pathname;
  } catch (e) {
    // If URL parsing fails, it's not a valid URL we're interested in.
    return false;
  }
};

// Function to check if the URL is the final login resume redirect
export const isLoginResumeRedirect = (url) => {
  // The backend redirects to /loginWithRedirect?t=TOKEN
  // Meteor's default resume path is often /login-resume/TOKEN or simila
  return url.includes('/loginWithRedirect?t=') || url.includes('/login-resume/');
};

export const parseTokenFromResumeUrl = (url) => {
  try {
    const params = new URLSearchParams(url.substring(url.indexOf("?") + 1));
    return params.get('t');
  } catch (error) {
    console.error('Error parsing token from resume URL:', error, 'URL:', url);
    return null;
  }
}; 