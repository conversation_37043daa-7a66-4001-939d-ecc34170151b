import {
  BASE_URLS,
  DATA_EXPLORER_AMOUNT_MONTHLY,
  DATA_EXPLORER_CONFIG,
  DATA_EXPLORER_LABEL,
  DATA_EXPLORER_LABEL_WEEKLY,
  DATA_EXPLORER_TABLE,
  DATA_EXPLORER_TABLE_GROSS,
  DATA_EXPLORER_TABLE_WEEKLY,
  DATA_EXPLORER_TABLE_YEARLY,
  DATA_EXPLORER_YAXIS,
  LOGIN_DATA
} from 'cypress/support/constants';
import DataExplorer from 'cypress/pages/data-explorer';

context('Express Drive Up', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.DATA_EXPLORER);
  });

  it('Should verify data displayed in data explorer section', () => {
    checkValuesInDataExplorer();
  });
});

const checkValuesInDataExplorer = () => {
  DataExplorer.reportOrgs.should('include.text', LOGIN_DATA.ORGNAME);

  //Check date range filtering
  DataExplorer.revenueDateRange.clear();
  DataExplorer.revenueDateRange.type(DATA_EXPLORER_CONFIG.DATE_RANGE);
  cy.get('.applyBtn').click();
  DataExplorer.tableLabel.should('be.visible');

  //Check bar chart axis and values
  for (let n = 0; n < 2; n++) {
    cy.get('.apexcharts-xaxis-label').eq(n).should('include.text', DATA_EXPLORER_LABEL[n].date);
  }
  for (let n = 0; n < 2; n++) {
    cy.get('.apexcharts-yaxis-label').eq(n).should('include.text', DATA_EXPLORER_YAXIS.MONTHLY[n].value);
  }
  for (let n = 0; n < 2; n++) {
    cy.get('.apexcharts-datalabels text').eq(n).should('include.text', DATA_EXPLORER_AMOUNT_MONTHLY[n].amount);
  }

  //Check values in table
  for (let n = 0; n < 2; n++) {
    DataExplorer.tableLabel.eq(n).should('include.text', DATA_EXPLORER_LABEL[n].date);
  }
  let dataIndex = 0;
  for (let n = 0; n < DATA_EXPLORER_TABLE.length; n++) {
    for (let m = 0; m < 3; m++) {
      DataExplorer.tableValue
        .eq(dataIndex)
        .should('include.text', DATA_EXPLORER_TABLE[n][Object.keys(DATA_EXPLORER_TABLE[n])[m]]);
      dataIndex++;
    }
  }

  //Check filter by revenue type
  DataExplorer.filterRevenueType.select(DATA_EXPLORER_CONFIG.GROSS);
  let dataIndexGross = 0;
  for (let n = 0; n < DATA_EXPLORER_TABLE_GROSS.length; n++) {
    for (let m = 0; m < 5; m++) {
      DataExplorer.tableValue
        .eq(dataIndexGross)
        .should('include.text', DATA_EXPLORER_TABLE_GROSS[n][Object.keys(DATA_EXPLORER_TABLE_GROSS[n])[m]]);
      dataIndexGross++;
    }
  }

  //Check filter by revenue source
  DataExplorer.filterRevenueType.select(DATA_EXPLORER_CONFIG.NET);
  DataExplorer.filterRevenueSource.select(DATA_EXPLORER_CONFIG.FAMILY);
  for (let n = 0; n < 2; n++) {
    DataExplorer.tableLabel.eq(n).should('include.text', DATA_EXPLORER_LABEL[n].date);
    DataExplorer.tableValue.eq(n).should('include.text', DATA_EXPLORER_TABLE[n].familyAmount);
  }

  DataExplorer.filterRevenueSource.select(DATA_EXPLORER_CONFIG.PAYER);
  for (let n = 0; n < 2; n++) {
    DataExplorer.tableLabel.eq(n).should('include.text', DATA_EXPLORER_LABEL[n].date);
    DataExplorer.tableValue.eq(n).should('include.text', DATA_EXPLORER_TABLE[n].payerAmount);
  }

  //Check grouping
  DataExplorer.filterRevenueSource.select(DATA_EXPLORER_CONFIG.BOTH);
  DataExplorer.revenueGrouping.select(DATA_EXPLORER_CONFIG.YEARLY);
  DataExplorer.tableLabel.should('be.visible');
  DataExplorer.tableLabel.should('include.text', DATA_EXPLORER_CONFIG.YEAR);
  const dataIndexYearly = 0;
  for (let n = 0; n < DATA_EXPLORER_TABLE_YEARLY.length; n++) {
    DataExplorer.tableValue
      .eq(dataIndexYearly)
      .should('include.text', DATA_EXPLORER_TABLE_YEARLY[0][Object.keys(DATA_EXPLORER_TABLE_YEARLY[0])[n]]);
  }

  DataExplorer.revenueGrouping.select(DATA_EXPLORER_CONFIG.WEEKLY);
  DataExplorer.tableLabel.should('be.visible');
  for (let n = 0; n < 9; n++) {
    DataExplorer.tableLabel.eq(n).should('include.text', DATA_EXPLORER_LABEL_WEEKLY[n].date);
  }
  let dataIndexWeekly = 0;
  for (let n = 0; n < DATA_EXPLORER_TABLE_WEEKLY.length; n++) {
    for (let m = 0; m < 3; m++) {
      DataExplorer.tableValue
        .eq(dataIndexWeekly)
        .should('include.text', DATA_EXPLORER_TABLE_WEEKLY[n][Object.keys(DATA_EXPLORER_TABLE_WEEKLY[n])[m]]);
      dataIndexWeekly++;
    }
  }

  //Check export to csv
  DataExplorer.revenueExportBtn.click();
  cy.readFile('cypress/downloads/export.csv').should('exist');
};
