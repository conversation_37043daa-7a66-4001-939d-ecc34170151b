import MediaGallery from 'cypress/pages/media-gallery';
import { BASE_URLS, MEDIAGALLERY_DATA } from 'cypress/support/constants';

context('Dashboard -> Media Gallery', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.MEDIAGALLERY);
  });

  it('should display media gallery containts', () => {
    pageLabelValidation();
    validationForFirstSetOfDates();
    validationForSecondSetOfDates();
    emptyImageValidation();
  });
});

const pageLabelValidation = () => {
  MediaGallery.mediaGalleryPageLabel.invoke('text').then((label) => {
    const containtsLabel = MEDIAGALLERY_DATA.LABELS.includes(label);
    expect(containtsLabel).to.be.true;
  });
  MediaGallery.mediaGalleryStartDateLabel.invoke('text').then((label) => {
    const containtsLabel = MEDIAGALLERY_DATA.LABELS.includes(label);
    expect(containtsLabel).to.be.true;
  });
  MediaGallery.mediaGalleryEndDateLabel.invoke('text').then((label) => {
    const containtsLabel = MEDIAGALLERY_DATA.LABELS.includes(label);
    expect(containtsLabel).to.be.true;
  });
  MediaGallery.mediaGalleryTypeLabel.invoke('text').then((label) => {
    const containtsLabel = MEDIAGALLERY_DATA.LABELS.includes(label);
    expect(containtsLabel).to.be.true;
  });
};
const validationForFirstSetOfDates = () => {
  MediaGallery.mediaGalleryStartDate.click().clear().type(MEDIAGALLERY_DATA.START_DATE);
  MediaGallery.mediaGalleryEndDate.click().clear().type(MEDIAGALLERY_DATA.END_DATE);
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE2);
  MediaGallery.mediaGalleryUpdateBtn.click();
  imagesValidation();
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE1);
  MediaGallery.mediaGalleryUpdateBtn.click();
  imagesValidation();
};
const validationForSecondSetOfDates = () => {
  MediaGallery.mediaGalleryStartDate.click().clear().type(MEDIAGALLERY_DATA.START_DATE2);
  MediaGallery.mediaGalleryEndDate.click().clear().type(MEDIAGALLERY_DATA.END_DATE2);
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE2);
  MediaGallery.mediaGalleryUpdateBtn.click();
  imagesValidation();
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE1);
  MediaGallery.mediaGalleryUpdateBtn.click();
  imagesValidation();
};
const imagesValidation = () => {
  MediaGallery.imageValidation.each(($el) => {
    cy.wrap($el).find('img').should('have.attr', 'src').and('not.be.empty');
    cy.wrap($el).find('img').should('have.attr', 'alt').and('not.be.empty');
    cy.wrap($el).find('img').should('be.visible');
    cy.wrap($el).find('img').should('have.attr', 'loading', 'lazy');
  });
};

const emptyImageValidation = () => {
  MediaGallery.mediaGalleryStartDate.click().clear().type(MEDIAGALLERY_DATA.START_DATE1);
  MediaGallery.mediaGalleryEndDate.click().clear().type(MEDIAGALLERY_DATA.END_DATE1);
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE2);
  MediaGallery.mediaGalleryUpdateBtn.click();
  MediaGallery.imageValidation.should('not.be.visible');
  MediaGallery.mediaGalleryType.select(MEDIAGALLERY_DATA.TYPE1);
  MediaGallery.mediaGalleryUpdateBtn.click();
  MediaGallery.imageValidation.should('not.be.visible');
};
