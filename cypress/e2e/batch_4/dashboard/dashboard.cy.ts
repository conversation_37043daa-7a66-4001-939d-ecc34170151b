import MySite from 'cypress/pages/my-site';
import Dashboard from 'cypress/pages/dashboard';
import {
  CHILD_DATA,
  MOMENTS_CONTENT,
  CHILD_DATA_ARRIVED,
  BASE_URLS,
  GROUPS,
  NEW_PLAN_CHILD,
  PAST_DUE_ACCOUNTS_WIDGET_DATA
} from 'cypress/support/constants';
import { getFullName } from '../../utils';
import People from 'cypress/pages/people';
import navigation from 'cypress/pages/navigation';
import moments from 'cypress/pages/moments';
import people from 'cypress/pages/people';
import checkinCheckout from 'cypress/pages/checkin-checkout';
import groups from 'cypress/pages/groups';
import expressDriveUp from 'cypress/pages/express-drive-up';
import billing from '../../../pages/billing';

context('Dashboard tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login();
    navigation.loadingSpinner.should('not.exist');
  });

  it('Check access and layout, widgets: latest activity, express drive-up and documents outstanding', () => {
    checkAccessAndLayout();
    checkCreatingMoment();
    checkLatestActivitySection();
    checkExpressDriveUpSection();
    checkOutstandingDocumentsWidget();
    checkPastDueAccountSection();
  });

  it('Check stats widget', () => {
    checkAbsentStats();
    checkCheckInStats();
  });
});

const checkAccessAndLayout = () => {
  cy.url().should('include', 'my-site');
  Dashboard.organizationName.should('be.visible');
  navigation.dashboardNav.should('be.visible');
  navigation.manageNav.should('be.visible');
  navigation.contentNav.should('be.visible');
  navigation.timeNav.should('be.visible');
  navigation.reportsNav.should('be.visible');
  navigation.billingNav.should('be.visible');
  navigation.adminNav.should('be.visible');
};

const checkCreatingMoment = () => {
  navigation.newMomentNavMenu.click();
  moments.momentType.select('Food');
  moments.foodMomentDinner.click();
  cy.wait(500);
  moments.tagPeopleDiv.type('Matt Coffman');
  cy.wait(500);
  moments.momentDetails.should('be.visible');
  moments.momentDateTimePicker.type('09:00');
  moments.momentComment.type('Test');
  moments.saveMomentButton.click();
};

const checkLatestActivitySection = () => {
  Dashboard.latestActivitySection.should('be.visible');
  MySite.timelineContentActivity.contains('Matt Coffman').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.newCheckoutButton.click();
  checkinCheckout.checkoutTime.type('10:00');
  checkinCheckout.checkoutModalSave.click();
  cy.containsOkAndClick();
  people.newCheckinButton.click();
  checkinCheckout.checkinTime.type('11:00');
  checkinCheckout.checkinModalSave.click();
  cy.containsOkAndClick();
  navigation.dashboardNav.click();
  for (let n = 0; n < 3; n++) {
    MySite.timelineBadgeColor.eq(n).should('have.css', 'color', MOMENTS_CONTENT[n].color);
    MySite.timelineContentActivity.eq(n).should('include.text', MOMENTS_CONTENT[n].content);
  }
};

const checkExpressDriveUpSection = () => {
  Dashboard.expressDriveUp.should('be.visible');
  Dashboard.peopleExpressDriveUp(CHILD_DATA.ID)
    .invoke('text')
    .then((text) => {
      expect(text).to.contain(getFullName(CHILD_DATA.FIRST_NAME, CHILD_DATA.LAST_NAME));
    });
  MySite.estimatedArrivalTime.should('be.visible');
  Dashboard.peopleExpressDriveUp(CHILD_DATA_ARRIVED.ID)
    .invoke('text')
    .then((text) => {
      expect(text).to.contain(getFullName(CHILD_DATA_ARRIVED.FIRST_NAME, CHILD_DATA_ARRIVED.LAST_NAME));
    });
  MySite.imHere.should('be.visible');
  Dashboard.expressDriveUpViewAllButton.should('be.visible').click();
  cy.url().should('include', 'express-drive-up');
  expressDriveUp.waitingForApproval.eq(0).realClick();
  cy.wait(1000);
  checkinCheckout.checkinModalSave.realClick();
  cy.containsOkAndClick();
  cy.wait(1000);
  expressDriveUp.waitingForApproval.eq(0).realClick();
  cy.wait(1000);
  checkinCheckout.checkinModalSave.realClick();
  cy.containsOkAndClick();
  cy.wait(1000);

  cy.visit(BASE_URLS.DEFAULT);
  cy.url().should('include', 'my-site');
  navigation.loadingSpinner.should('not.exist');
  Dashboard.peopleExpressDriveUp(CHILD_DATA.ID).should('not.exist');
  Dashboard.peopleExpressDriveUp(CHILD_DATA_ARRIVED.ID).should('not.exist');
};

const checkOutstandingDocumentsWidget = () => {
  Dashboard.documentsOustandingWidget.should('be.visible');

  cy.get('#Documents-Outstanding').within(() => {
    Dashboard.listItemLabel(CHILD_DATA.ID).should(
      'have.text',
      getFullName(CHILD_DATA.FIRST_NAME, CHILD_DATA.LAST_NAME)
    );

    Dashboard.listItemValue(CHILD_DATA.ID).should('have.text', CHILD_DATA.INITIAL_OUTSTANDING_DOCUMENTS);

    Dashboard.listItemLabel(CHILD_DATA.ID).click();
  });
  People.firstNameLastName.invoke('text').then((text) => {
    expect(text).to.contain(getFullName(CHILD_DATA.FIRST_NAME, CHILD_DATA.LAST_NAME));
  });
  People.documentsActionsButtons.eq(0).click();
  People.approveCompletedDocument.eq(0).click();

  cy.contains('Document Approved', { timeout: 3000 }).should('exist');
  cy.containsOkAndClick();

  navigation.dashboardNav.click();
  navigation.loadingSpinner.should('not.exist');

  const newOutStandingDocuments = parseInt(CHILD_DATA.INITIAL_OUTSTANDING_DOCUMENTS) - 1;
  cy.get('#Documents-Outstanding').within(() => {
    Dashboard.listItemValue(CHILD_DATA.ID)
      .invoke('text')
      .then((text) => {
        expect(text).to.equal(`${newOutStandingDocuments}`);
      });
  });
};

const checkPastDueAccountSection = () => {
  Dashboard.pastDueAccountSection.should('be.visible');
  cy.get('#Past-Due-Accounts')
    .find('tbody tr')
    .should('have.length', PAST_DUE_ACCOUNTS_WIDGET_DATA.ROWS.length)
    .each((row, index) => {
      const expected = PAST_DUE_ACCOUNTS_WIDGET_DATA.ROWS[index];
      cy.wrap(row).within(() => {
        cy.get('a').should('have.text', expected.name);
        cy.get('span').should('have.text', expected.value);
      });
    });
};

const checkAbsentStats = () => {
  MySite.absentPeopleCount.should('have.text', '1');
  navigation.manageNav.click({ force: true });
  navigation.groupsNav.click({ force: true });
  groups.groupRosterBtn.eq(0).click();
  MySite.checkinoutButton.eq(0).should('include.text', 'Absent');
};

const checkCheckInStats = () => {
  //Adding new children absent
  cy.visit(BASE_URLS.PEOPLE_DIRECTORY);
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type('DE1');
  people.lastNameInput.type('DE1');
  people.defaultGroupInput.select(GROUPS.KINDERGARDEN.NAME);
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  people.schedulingTab.click();
  people.newScheduleItem.click();
  people.scheduleEntryBody.click();
  people.scheduleTargetGroup.select(GROUPS.KINDERGARDEN.NAME);
  people.scheduleSaturday.check({ force: true });
  people.scheduleSunday.check({ force: true });
  billing.selectPlanName.select(NEW_PLAN_CHILD.PLAN_NAME, { force: true });
  people.saveScheduleEntry.click();
  cy.containsOkAndClick();
  people.scheduleViewAll.click();
  cy.get(".btn.btn-icon.btn-clean[data-toggle='dropdown']").eq(0).click({ force: true });
  cy.get('span.dropdown-item.clickable-row.cancelReservationLink').eq(0).click({ force: true });
  cy.get("button.swal2-confirm.btn.btn-primary.font-weight-bolder:contains('Cancel Instance')", {
    timeout: 1000
  }).click();
  cy.get('#cancelReservation', { timeout: 1000 }).click();
  people.firstNameLastName.should('be.visible');

  //Adding new children checked in
  cy.visit(BASE_URLS.PEOPLE_DIRECTORY);
  people.addPersonButton.should('be.visible').click();
  people.firstNameInput.type('DE2');
  people.lastNameInput.type('DE2');
  people.defaultGroupInput.select(GROUPS.KINDERGARDEN.NAME);
  people.modalSaveButton.click();
  people.firstNameLastName.should('be.visible');
  people.newCheckinButton.click();
  checkinCheckout.checkinTime.type('11:00');
  checkinCheckout.checkinModalSave.click();
  cy.containsOkAndClick();

  navigation.dashboardNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  MySite.checkInPeopleCount.should('have.text', '1');
  MySite.enrolledPeopleCount.should('have.text', '5');
  MySite.checkInStaffCount.should('have.text', '3');
  MySite.grossCapacity.should('be.visible');
  MySite.netCapacity.should('be.visible');
};
