import mediaReview from 'cypress/pages/media-review';
import moments from 'cypress/pages/moments';
import navigation from 'cypress/pages/navigation';
import { BASE_URLS, MEDIA_REVIEW } from 'cypress/support/constants';
import 'cypress-wait-until';

context('Validate Media review page', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.MEDIA_REVIEW_URL);
  });

  it('should verify all content on media review page', () => {
    createMomentAndUploadMedia();
    verifyMediaReviewPageContent();
    allMediaReviewActions(MEDIA_REVIEW.APPROVE_ACTION);
    validateAfterActionPaginationCount();
    createMomentAndUploadMedia();
    allMediaReviewActions(MEDIA_REVIEW.REJECT_ACTION);
    validateAfterActionPaginationCount();
  });
});

function verifyMediaReviewPageContent() {
  cy.waitUntil(() => navigation.mediaReviewNav.invoke('text')).should('eq', MEDIA_REVIEW.MEDIA_REVIEW_LABEL);
  cy.waitUntil(() => mediaReview.mediaReviewCardHeading.invoke('text')).should('eq', MEDIA_REVIEW.MEDIA_REVIEW_LABEL);
  cy.waitUntil(() => mediaReview.mediaReviewTable.find('tr'))
    .eq(0)
    .within(() => {
      verifyButton(mediaReview.momentDetailsButton, MEDIA_REVIEW.MOMENT_DETAILS_BUTTON);
      verifyButton(mediaReview.approveAllButton, MEDIA_REVIEW.APPROVE_ALL_BUTTON);
      verifyButton(mediaReview.rejectAllButton, MEDIA_REVIEW.REJECT_ALL_BUTTON);
    });
}

function verifyButton(buttonSelector, expectedText) {
  buttonSelector
    .first()
    .invoke('text')
    .then((text) => text.replaceAll(/\n/g, '').trim())
    .should('eq', expectedText);
}

function createMomentAndUploadMedia() {
  navigation.newMomentNavMenu.click();
  cy.get('[data-cy="tag-people"] .tagify__input').as('tagInput').invoke('show').type(MEDIA_REVIEW.TAGGED_PEOPLE);

  moments.momentFilesInput.invoke('show').attachFile(MEDIA_REVIEW.MEDIA_NAMES).invoke('hide');

  cy.wait(5000);
  moments.saveMomentButton.click();
}

function validateAfterActionPaginationCount() {
  cy.visit(BASE_URLS.MEDIA_REVIEW_URL);
  navigation.loadingSpinner.should('not.exist');
  mediaReview.paginationItemCount
    .invoke('text')
    .then(recordCount)
    .then(([_, afterEnd]) => {
      expect(parseInt(afterEnd)).to.eq(0);
    });
}

function allMediaReviewActions(actionType: string) {
  cy.visit(BASE_URLS.MEDIA_REVIEW_URL);
  navigation.loadingSpinner.should('not.exist');

  mediaReview.paginationItemCount
    .invoke('text')
    .then(recordCount)
    .then(([_, endCount]) => {
      expect(parseInt(endCount)).to.be.gt(0);

      switch (actionType) {
        case MEDIA_REVIEW.APPROVE_ACTION:
          clickActionButton(mediaReview.approveAllButton);
          break;
        case MEDIA_REVIEW.REJECT_ACTION:
          clickActionButton(mediaReview.rejectAllButton);
          break;
      }
    });
}

function clickActionButton(buttonSelector) {
  buttonSelector.then((btn) => {
    cy.wrap(btn).click({ multiple: true });
  });
}

function recordCount(countTxt: string): string[] {
  return countTxt.trim().split('-');
}
