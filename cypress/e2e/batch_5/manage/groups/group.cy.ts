import { BASE_URLS } from 'cypress/support/constants';
import groups from '../../../../pages/groups';
import moments from 'cypress/pages/moments';
import navigation from 'cypress/pages/navigation';

context('Groups page tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.GROUPS);
  });

  it('Checks adding new group, sorting, groups detail and configuration', () => {
    createNewGroup();
  });
});

const createNewGroup = () => {
  cy.get('.container .card.card-custom.gutter-b').should('have.length', 6);
  //Add new group
  groups.addGroupBtn.click();
  groups.addGroupName.type('A New Group');
  groups.addGroupCapacity.type('2');
  groups.infantGroupCheckbox.check({ force: true });
  groups.saveGroupButton.click();
  cy.containsOkAndClick();
  groups.settingsGroup.eq(0).click();
  groups.groupConfiguration.eq(0).click();
  groups.groupPreferredCapacity.should('be.visible').type('2');
  groups.dessignateAsClassroom.check({ force: true });
  groups.groupRatio.should('be.visible').type('2');
  groups.saveGroupConfiguration.realClick();
  cy.containsOkAndClick();

  //Check settings buttons
  cy.visit(BASE_URLS.GROUPS);
  navigation.loadingSpinner.should('not.exist');
  cy.get('.container .card.card-custom.gutter-b').should('have.length', 7);
  groups.settingsGroup.eq(0).click({ force: true });
  groups.newMomentGroup.eq(0).click({ force: true });
  moments.momentType.should('be.visible');
  moments.closeMomentModal.click();
  groups.settingsGroup.eq(0).click();
  groups.groupBillingPlan.eq(0).click();
  groups.groupDefaultBillingPlan.should('include.text', 'A New Group Default Billing Plan');
  groups.groupCloseDefaultBillingPlan.click();
  groups.groupDefaultBillingPlan.should('not.exist');
  groups.settingsGroup.eq(0).click();
  groups.groupRules.eq(0).click();
  groups.groupSuggestionRules.should('include.text', 'A New Group Suggestion Rules');
  groups.closeSuggestionRules.click();
  groups.groupSuggestionRules.should('not.exist');
  groups.settingsGroup.eq(0).click();
  groups.groupPeekWeek.eq(0).should('be.visible');
  groups.modifySortingBtn.click();
  groups.sortingMessage.should('be.visible');
  groups.finishSortingBtn.click();

  //Checks group deletion
  groups.settingsGroup.eq(0).click();
  groups.deleteGroup.eq(0).click();
  cy.contains('Yes, delete it!').click();
  cy.get('.container .card.card-custom.gutter-b', { timeout: 3000 }).should('have.length', 6);
};
