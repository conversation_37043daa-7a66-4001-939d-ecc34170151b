import {
  getCustomFormattedTodayDate,
  getFormattedDatesForActivities,
  getFormattedFutureDate,
  getSecondNextMondayWithPreviousDate
} from 'cypress/e2e/utils';
import admin from 'cypress/pages/admin';
import billing from 'cypress/pages/billing';
import people from 'cypress/pages/people';
import registration from 'cypress/pages/registration';
import {
  BASE_URLS,
  CHILD_DATA,
  COUPON_DATA_BUNDLE,
  PLAN_DATA_BUNDLE_1,
  PLAN_DATA_BUNDLE_2,
  SCALED_AMOUNTS_BUNDLE
} from 'cypress/support/constants';

context('Bundle tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.BILLING_ADMIN_PLANS_URL);
  });

  it('Check bundle on parent account', () => {
    addPlansAndBundle();
    checkBundleAsParent();
  });
});

const addPlansAndBundle = () => {
  //First Plan
  billing.addPlanBtn.click();
  // Close notification box for no api key if it exists
  cy.get('.tox-notification__dismiss')
    .its('length')
    .then((length) => {
      if (length > 0) {
        cy.get('.tox-notification__dismiss').click();
      }
    });
  billing.addPlanDescription.scrollIntoView().click({ force: true }).type(PLAN_DATA_BUNDLE_1.PLAN_NAME);
  billing.addPlanCategory.select(PLAN_DATA_BUNDLE_1.PLAN_CATEGORY);

  billing.addPlanProgram.select(PLAN_DATA_BUNDLE_1.PLAN_PROGRAM);
  billing.addPlanFrequency.select(PLAN_DATA_BUNDLE_1.PLAN_FREQUENCY);
  billing.scaledAmount.eq(0).clear();
  billing.scaledAmount.eq(0).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_1);
  billing.scaledAmount.eq(1).clear();
  billing.scaledAmount.eq(1).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_2);
  billing.scaledAmount.eq(2).clear();
  billing.scaledAmount.eq(2).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_3);
  billing.scaledAmount.eq(3).clear();
  billing.scaledAmount.eq(3).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_4);
  billing.scaledAmount.eq(4).clear();
  billing.scaledAmount.eq(4).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_5);
  billing.scaledAmount.eq(5).clear();
  billing.scaledAmount.eq(5).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_6);
  billing.scaledAmount.eq(6).clear();
  billing.scaledAmount.eq(6).type(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_7);

  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(getFormattedFutureDate(40), { force: true });
  cy.get('td.active.day').click();
  cy.wait(1000);
  billing.addPlanLedgerAccount.click();
  billing.addPlanLedgerAccount.type(PLAN_DATA_BUNDLE_1.PLAN_LEDGER_ACCOUNT);
  billing.addPlanDateType.select(PLAN_DATA_BUNDLE_1.PLAN_DATE_TYPE);
  billing.addPlanServiceDates
    .find('option')
    .eq(1)
    .then(function ($option) {
      billing.addPlanServiceDates.select($option.val()).trigger('change');
    });

  billing.addPlanStartTime.type(PLAN_DATA_BUNDLE_1.PLAN_START_TIME);
  billing.addPlanEndTime.type(PLAN_DATA_BUNDLE_1.PLAN_END_TIME);
  billing.addPlanRegStartDate.type(PLAN_DATA_BUNDLE_1.PLAN_REG_START_DATE, {
    force: true
  });

  cy.get('td.active.day').click();
  billing.addPlanRegEndDate.type(PLAN_DATA_BUNDLE_1.PLAN_REG_END_DATE, { force: true });
  cy.get('td.active.day').click();
  billing.addPlanGrades.select(PLAN_DATA_BUNDLE_1.PLAN_GRADES, { force: true });
  billing.addPlanScheduleType.select(PLAN_DATA_BUNDLE_1.PLAN_SCHEDULE_TYPE);
  billing.addPlanRegFreeExempt.check({ force: true });

  admin.saveEdits.click({ force: true });
  admin.saveEdits.should('not.exist');

  //Second plan
  billing.addPlanBtn.click();
  cy.get('.tox-notification__dismiss')
    .its('length')
    .then((length) => {
      if (length > 0) {
        cy.get('.tox-notification__dismiss').click();
      }
    });
  billing.addPlanDescription.scrollIntoView().click({ force: true }).type(PLAN_DATA_BUNDLE_2.PLAN_NAME);
  billing.addPlanCategory.select(PLAN_DATA_BUNDLE_2.PLAN_CATEGORY);

  billing.addPlanProgram.select(PLAN_DATA_BUNDLE_2.PLAN_PROGRAM);
  billing.addPlanFrequency.select(PLAN_DATA_BUNDLE_2.PLAN_FREQUENCY);
  billing.scaledAmount.eq(0).clear();
  billing.scaledAmount.eq(0).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_1);
  billing.scaledAmount.eq(1).clear();
  billing.scaledAmount.eq(1).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_2);
  billing.scaledAmount.eq(2).clear();
  billing.scaledAmount.eq(2).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_3);
  billing.scaledAmount.eq(3).clear();
  billing.scaledAmount.eq(3).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_4);
  billing.scaledAmount.eq(4).clear();
  billing.scaledAmount.eq(4).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_5);
  billing.scaledAmount.eq(5).clear();
  billing.scaledAmount.eq(5).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_6);
  billing.scaledAmount.eq(6).clear();
  billing.scaledAmount.eq(6).type(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_7);

  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(getFormattedFutureDate(40), { force: true });
  cy.get('td.active.day').click();
  cy.wait(1000);
  billing.addPlanLedgerAccount.click();
  billing.addPlanLedgerAccount.type(PLAN_DATA_BUNDLE_2.PLAN_LEDGER_ACCOUNT);
  billing.addPlanDateType.select(PLAN_DATA_BUNDLE_2.PLAN_DATE_TYPE);
  billing.addPlanServiceDates
    .find('option')
    .eq(1)
    .then(function ($option) {
      billing.addPlanServiceDates.select($option.val()).trigger('change');
    });

  billing.addPlanStartTime.type(PLAN_DATA_BUNDLE_2.PLAN_START_TIME);
  billing.addPlanEndTime.type(PLAN_DATA_BUNDLE_2.PLAN_END_TIME);
  billing.addPlanRegStartDate.type(PLAN_DATA_BUNDLE_2.PLAN_REG_START_DATE, {
    force: true
  });

  cy.get('td.active.day').click();
  billing.addPlanRegEndDate.type(PLAN_DATA_BUNDLE_2.PLAN_REG_END_DATE, { force: true });
  cy.get('td.active.day').click();
  billing.addPlanGrades.select(PLAN_DATA_BUNDLE_2.PLAN_GRADES, { force: true });
  billing.addPlanScheduleType.select(PLAN_DATA_BUNDLE_2.PLAN_SCHEDULE_TYPE);
  billing.addPlanRegFreeExempt.check({ force: true });

  admin.saveEdits.click({ force: true });
  admin.saveEdits.should('not.exist');

  //Add bundle
  billing.addPlanBtn.click();
  cy.get('.tox-notification__dismiss')
    .its('length')
    .then((length) => {
      if (length > 0) {
        cy.get('.tox-notification__dismiss').click();
      }
    });
  billing.checkBundle.check({ force: true });
  cy.wait(1000);

  billing.bundlePlanNumber('0').select(PLAN_DATA_BUNDLE_1.PLAN_NAME);
  billing.bundlePlanNumber('1').select(PLAN_DATA_BUNDLE_2.PLAN_NAME);

  SCALED_AMOUNTS_BUNDLE.forEach((amountRow, rowIndex) => {
    amountRow.forEach((value, colIndex) => {
      const inputName = `scaledAmount_${rowIndex}_${colIndex}`;
      cy.get(`input[name="${inputName}"]`).clear().type(value);
    });
  });

  billing.addPlanRegFreeExempt.check({ force: true });
  admin.saveEdits.click({ force: true });
  admin.saveEdits.should('not.exist');
  cy.performLogout();
};

const checkBundleAsParent = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY, 'Parent');

  //Check bundle amounts
  people.personName.contains(CHILD_DATA.FIRST_NAME).click();
  people.programsHeaderNavigation.should('be.visible');
  people.programsHeaderNavigation.click();
  registration.plans.eq(5).click();
  people.addProgramsContinue.click();
  people.planAmount.should('include.text', PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_7);

  people.addMissingPlanBtn.click();
  const regularPrice = Number(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_7) + Number(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_7);
  const yourSavingsAmount =
    Number(PLAN_DATA_BUNDLE_1.PLAN_AMOUNT_WEEK_7) +
    Number(PLAN_DATA_BUNDLE_2.PLAN_AMOUNT_WEEK_7) -
    Number(SCALED_AMOUNTS_BUNDLE[6][6]);
  people.regularPriceAmount.should('include.text', `$${regularPrice}.00`);
  people.bundledPriceAmount.should('include.text', `$${SCALED_AMOUNTS_BUNDLE[6][6]}.00`);
  people.yourSavingsAmount.should('include.text', `$${yourSavingsAmount}.00`);
  cy.wait(2000);
  people.addProgramsContinue.scrollIntoView().realClick();
  people.addProgramsContinue.should('not.exist');

  people.futureChargesAmount.should('include.text', `$${SCALED_AMOUNTS_BUNDLE[6][6]}.00`);
  people.totalAmountToPay.should('include.text', `$${SCALED_AMOUNTS_BUNDLE[6][6]}.00`);

  //Check adding coupons to bundle
  registration.addDiscountBtn.realHover();
  registration.couponDiscount.click({ force: true });
  people.couponCodeName.type(COUPON_DATA_BUNDLE.CODE);
  people.applyCouponBtn.click();
  cy.containsOkAndClick();

  people.dueTodayAmount.should('include.text', '$0.00');
  people.futureChargesAmount.should('include.text', '$0.00');
  people.totalAmountToPay.should('include.text', '$0.00');

  //Check programs in child profile
  people.addProgramsSubmit.click({ force: true });
  cy.containsOkAndClick();

  const dates = getFormattedDatesForActivities();
  const tomorrow = dates.tomorrow;
  const { secondNextMonday, previousDay } = getSecondNextMondayWithPreviousDate(true);

  const todayFormatted = getCustomFormattedTodayDate(true);

  people.enrolledProgram.should('have.length', 2);

  people.enrolledProgramsStartDate.eq(0).should('include.text', todayFormatted);
  people.enrolledProgramsDays
    .eq(0)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R')
    .should('contain', 'F');
  people.enrolledProgramsType.eq(0).should('include.text', PLAN_DATA_BUNDLE_1.PLAN_SCHEDULE_TYPE);

  people.enrolledProgramsStartDate.eq(1).should('include.text', todayFormatted);
  people.enrolledProgramsDays
    .eq(1)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R')
    .should('contain', 'F');
  people.enrolledProgramsType.eq(1).should('include.text', PLAN_DATA_BUNDLE_2.PLAN_SCHEDULE_TYPE);

  //Check first part of the bundle
  people.enrolledProgram.eq(0).each(($card) => {
    cy.wrap($card).within(() => {
      const dataId = $card.attr('data-id');
      people.editEnrolledProgramBtn(dataId).should('be.visible').click();
      cy.wait(500);
    });
    people.planDescription.should('have.length', 2);
    people.planAmountBundle.should('have.length', 2);
    for (let n = 0; n < 2; n++) {
      people.planAmountBundle.eq(n).should('include.text', `$${SCALED_AMOUNTS_BUNDLE[6][6]}.00`);
      registration.startDatePicker
        .eq(n)
        .invoke('val')
        .then((value) => {
          const regex = new RegExp(tomorrow);
          expect(value).to.match(regex);
        });
    }
  });
  people.cancelEditEnrollmentProgram.click();
  cy.wait(500);

  //Check second part of the bundle
  people.enrolledProgram.eq(1).each(($card) => {
    cy.wrap($card).within(() => {
      const dataId = $card.attr('data-id');
      people.editEnrolledProgramBtn(dataId).should('be.visible').click();
      cy.wait(500);
    });
    people.planDescription.should('have.length', 2);
    people.planAmountBundle.should('have.length', 2);
    for (let n = 0; n < 2; n++) {
      people.planAmountBundle.eq(n).should('include.text', `$${SCALED_AMOUNTS_BUNDLE[6][6]}.00`);
      registration.startDatePicker
        .eq(n)
        .invoke('val')
        .then((value) => {
          const regex = new RegExp(tomorrow);
          expect(value).to.match(regex);
        });
    }
  });
  people.cancelEditEnrollmentProgram.click();

  //Check plan edition
  cy.get('[data-cy^="edit-enrolled-program-"]').eq(0).click();
  registration.fridayCheckbox.eq(0).uncheck();
  people.decreaseDaysProgram.should('have.length', 1);
  registration.startDatePicker
    .eq(0)
    .invoke('val')
    .then((value) => {
      const regex = new RegExp(secondNextMonday);
      expect(value).to.match(regex);
    });
  people.planAmountBundle.eq(0).should('include.text', `$${SCALED_AMOUNTS_BUNDLE[5][6]}.00`);
  people.saveMoreText.should('have.length', 1);

  registration.fridayCheckbox.eq(1).uncheck();
  people.decreaseDaysProgram.should('have.length', 2);
  people.saveMoreText.should('have.length', 2);
  registration.startDatePicker
    .eq(1)
    .invoke('val')
    .then((value) => {
      const regex = new RegExp(secondNextMonday);
      expect(value).to.match(regex);
    });
  people.planAmountBundle.eq(1).should('include.text', `$${SCALED_AMOUNTS_BUNDLE[5][5]}.00`);

  //Check edited plans
  people.saveEditEnrollmentProgramBtn.click();
  cy.containsOkAndClick();
  people.enrolledProgram.should('have.length', 4);

  people.enrolledProgramsStartDate.eq(0).should('include.text', todayFormatted);
  people.enrolledProgramsEndDate.eq(0).should('include.text', previousDay);
  people.enrolledProgramsDays
    .eq(0)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R')
    .should('contain', 'F');
  people.enrolledProgramsType.eq(0).should('include.text', PLAN_DATA_BUNDLE_1.PLAN_SCHEDULE_TYPE);

  people.enrolledProgramsStartDate.eq(1).should('include.text', todayFormatted);
  people.enrolledProgramsEndDate.eq(1).should('include.text', previousDay);
  people.enrolledProgramsDays
    .eq(1)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R')
    .should('contain', 'F');
  people.enrolledProgramsType.eq(1).should('include.text', PLAN_DATA_BUNDLE_2.PLAN_SCHEDULE_TYPE);

  people.enrolledProgramsStartDate.eq(2).should('include.text', secondNextMonday);
  people.enrolledProgramsDays
    .eq(2)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R');
  people.enrolledProgramsType.eq(2).should('include.text', PLAN_DATA_BUNDLE_1.PLAN_SCHEDULE_TYPE);

  people.enrolledProgramsStartDate.eq(3).should('include.text', secondNextMonday);
  people.enrolledProgramsDays
    .eq(3)
    .should('contain', 'M')
    .should('contain', 'T')
    .should('contain', 'W')
    .should('contain', 'R');
  people.enrolledProgramsType.eq(3).should('include.text', PLAN_DATA_BUNDLE_2.PLAN_SCHEDULE_TYPE);
};
