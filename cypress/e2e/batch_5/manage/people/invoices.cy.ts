import people from 'cypress/pages/people';
import { BASE_URLS } from 'cypress/support/constants';

context('People Transaction Invoices', () => {
    beforeEach(() => {
        cy.setDefaultDatabase();
        cy.login(BASE_URLS.PEOPLE_DIRECTORY);
    });

    it('Should verify the open invoices in the people transactions ', () => {
        people.peopleFilter.click();
        people.selectPeopleCheckbox.check({ force: true });
        people.selectFamilyCheckbox.uncheck({ force: true });
        cy.wait(1000);
        cy.get('[data-cy="people-item"]').first().click();
        people.transactionsHeaderNav.click();
        cy.get('input[name="invoices-date-range"]')
            .clear()
            .type('01/01/2020 - 01/31/2025', { force: true })
            .blur();
        cy.get('.applyBtn.btn.btn-sm.btn-primary').click();
        cy.get('span.text-dark-75.font-weight-bolder.mb-1.font-size-lg')
            .should('contain', 'due');
    });
});


