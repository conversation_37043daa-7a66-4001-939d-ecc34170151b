import { BASE_URLS, CUSTOMER_CREDITS_AND_DEPOSITS, TUTOR_DATA } from 'cypress/support/constants';
import Billing from 'cypress/pages/billing';
import { getPaginationCount } from 'cypress/e2e/utils';
import search from 'cypress/pages/search';
import navigation from 'cypress/pages/navigation';

context('Manage Bank Deposits Report', () => {
  beforeEach(() => {
    cy.login();
  });

  it('verify Manage Bank Deposits details', () => {
    search.searchPeopleIcon.click();
    search.searchPeopleInp.clear().type(TUTOR_DATA.FIRST_NAME);
    search.searchManageButton.first().click();
    Billing.billingTabParents.click();
    totalCreditAmountValidation();

    cy.visit(BASE_URLS.MANAGE_DEPOSITS_REPORT);
    navigation.loadingSpinner.should('not.exist');
    Billing.depositeDateRange.clear().type(CUSTOMER_CREDITS_AND_DEPOSITS.OPEN_DATE_RANGE);
    Billing.manageBankDepositeApplyBtn.click();
    Billing.manageBankDepositsUpdateBtn.click();
    cy.wait(10000);
    Billing.depositsReportTable.should('be.visible');
    verifyOpenDepositsDetails();
    verifyTotals();
    Billing.pastDepositsBtn.click();
    Billing.pastDepositsDateRange.clear().type(CUSTOMER_CREDITS_AND_DEPOSITS.PAST_DATE_RANGE);
    Billing.manageBankDepositeApplyBtn.last().click({ multiple: true });
    Billing.pastDepositsUpdateBtn.click();
    verifyPastDepositsDetails();
    verifyActions();
  });
});

const verifyPastDepositsDetails = () => {
  Billing.pastDepositsReportTable.each(($row) => {
    const pastPaymentDate = $row.find('[data-cy=past-deposits-date-column]');
    cy.wrap(pastPaymentDate).should('not.be.empty');

    const pastPaymentAmount = $row.find('[data-cy=past-deposits-total-amount-column]');
    cy.wrap(pastPaymentAmount).should('not.be.empty');
  });
};

const verifyOpenDepositsDetails = () => {
  Billing.depositsReportTable.each(($row) => {
    const paymentType = $row.find('[data-cy=payment-method-types]');
    cy.wrap(paymentType).should('not.have.text', 'credit card');

    const paymentAmount = $row.find('[data-cy=deposits-report-amount]');
    cy.wrap(paymentAmount).should('not.be.empty');

    const payerByName = $row.find('[data-cy=payer-by-name]');
    cy.wrap(payerByName).should('be.visible');

    const dateColumn = $row.find('[data-cy=deposits-payment-table-date]');
    cy.wrap(dateColumn).should('not.be.empty');
  });
};

const totalCreditAmountValidation = () => {
  Billing.totalCreditBalanceAmount.then(($el) => {
    const [start, end] = getPaginationCount($el.text());
    expect(start).to.eql(CUSTOMER_CREDITS_AND_DEPOSITS.TOTAL_CREDIT_BALANCE_AMOUNT);
  });
};

const getActionValue = (actionText: string): [string, string] => {
  const countTokens = actionText.trim().split(' ');
  const startIndex = String(countTokens[0]);
  const endIndex = String(countTokens[1]);
  return [startIndex, endIndex];
};

const verifyActions = () => {
  Billing.pastDepositsActionColumn.each(($el) => {
    const [start, end] = getActionValue($el.text());
    expect(start).to.eq('View/Edit');
  });
};

const verifyTotals = () => {
  // const parseCurrency = (value) => {
  //   return parseFloat(value.replace(/[$,]/g, "")); // Remove $ and commas, then convert to number
  // }
  // let total = 0;
  // Billing.depositsReportPaymentAmount.each(($el) => {
  //   total += parseCurrency($el.text());
  // });
  const total = '$400.00';

  // Nothing is checked at the start
  Billing.depositsReportTotalAmount.should('not.exist');

  // Click the select all checkbox
  Billing.depositsReportSelectAll.click({ force: true });
  // Verify the total amount is still the total amount
  Billing.depositsReportTotalAmount.contains(total);

  // Click the select all checkbox
  Billing.depositsReportSelectAll.click({ force: true });
  // Verify the total amount cell is now gone
  Billing.depositsReportTotalAmount.should('not.exist');

  // Click the select all checkbox
  Billing.depositsReportSelectAll.click({ force: true });
  // Verify the total amount is still the correct total amount
  Billing.depositsReportTotalAmount.contains(total);
};
