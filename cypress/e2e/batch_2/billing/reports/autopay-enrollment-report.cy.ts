import Relationships from 'cypress/pages/relationships';
import People from 'cypress/pages/people';
import { BASE_URLS, CHILD_DATA, PAYMENT_CARD_DATA, TUTOR_DATA, LOGIN_DATA } from 'cypress/support/constants';
import Billing from 'cypress/pages/billing';
import search from 'cypress/pages/search';
import navigation from 'cypress/pages/navigation';

context('Autopay Billing Report', () => {
  beforeEach(() => {
    cy.login();
  });

  it('verify person enrollment date and autopay details are not empty', () => {
    search.searchPeopleIcon.click();
    search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);

    search.searchManageButton.first().click();
    People.profileHeaderNavigation.click();

    People.firstNameInput.should('have.value', CHILD_DATA.FIRST_NAME);
    People.lastNameInput.should('have.value', CHILD_DATA.LAST_NAME);
    People.newPersonInputType.should('have.value', CHILD_DATA.TYPE);
    People.enrollmentDate.should('not.be.null');

    Relationships.relationshipHeaderNavigation.click();
    People.associatedParentTab.last().click();
    People.firstNameInput.should('have.value', TUTOR_DATA.FIRST_NAME);
    People.lastNameInput.should('have.value', TUTOR_DATA.LAST_NAME);
    People.newPersonInputType.should('have.value', TUTOR_DATA.TYPE);
    Billing.billingTabParents.click();
    const fourDigits = PAYMENT_CARD_DATA.CARD_NUMBER.substring(PAYMENT_CARD_DATA.CARD_NUMBER.length - 4);
    Billing.cardLastFourDigits.contains(fourDigits);

    cy.visit(BASE_URLS.AUTOPAY_ENROLLMENT_REPORT);
    navigation.loadingSpinner.should('not.exist');
    Billing.billingUpdateButton.click();
    Billing.enrollmentStatus
      .contains('Enrolled')
      .next('td')
      .then(($el) => {
        expect($el.text()).contains('Autopay');
      });
    cy.get('[data-cy="report-orgs"]')
      .find('option:selected')
      .should('have.value', LOGIN_DATA.ORGID);
  });
});
