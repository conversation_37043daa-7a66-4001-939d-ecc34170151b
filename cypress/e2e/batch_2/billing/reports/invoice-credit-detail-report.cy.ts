import billing from 'cypress/pages/billing';
import reports from 'cypress/pages/reports';
import { BASE_URLS, INVOICE_CREDIT_DETAIL_REPORT_DATA, INVOICE_CREDIT_ROWS } from 'cypress/support/constants';

context('Invoice Credit Details Report Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.INVOICE_CREDIT_DETAIL_REPORT);
  });

  it('Should check invoice credit detail report creation and functionalities', () => {
    checkInvoiceCreditDetailTable();
    checkFilteringOptions();
    checkExportCSV();
  });
});

const checkInvoiceCreditDetailTable = () => {
  reports.exportBtn.should('not.exist');
  reports.updateBtn.click();
  billing.totalAmountInvoiceCredit.should('not.exist');

  //Filtering by Date
  reports.startDateInput.clear().type(INVOICE_CREDIT_DETAIL_REPORT_DATA.START_DATE);
  reports.updateBtn.click();
  billing.totalAmountInvoiceCredit.should('be.visible');
  billing.totalAmountInvoiceCredit.should(
    'include.text',
    INVOICE_CREDIT_DETAIL_REPORT_DATA.TOTAL_INVOICE_CREDIT_AMOUNT_FIRST
  );

  //Checking table accuracy
  INVOICE_CREDIT_ROWS.forEach((expected, index) => {
    billing.dateInvoiceCredit.eq(index).should('have.text', expected.date);
    billing.typeInvoiceCredit.eq(index).should('have.text', expected.type);
    billing.descriptionInvoiceCredit.eq(index).should('have.text', expected.description);
    billing.forInvoiceCredit.eq(index).invoke('text').should('eq', expected.for);
    billing.checkNumberInvoice.eq(index).should('have.text', expected.checkNumber);
    billing.periodInvoiceCredit.eq(index).should('have.text', expected.period);
    billing.invoiceCreditAmount.eq(index).should('have.text', expected.amount);
  });
};

const checkFilteringOptions = () => {
  //Filtering by search
  billing.searchEntries.type(INVOICE_CREDIT_DETAIL_REPORT_DATA.DESCRIPTION);
  reports.updateBtn.click();
  billing.totalAmountInvoiceCredit.should('be.visible');
  billing.totalAmountInvoiceCredit.should(
    'include.text',
    INVOICE_CREDIT_DETAIL_REPORT_DATA.TOTAL_INVOICE_CREDIT_AMOUNT_SECOND
  );
  billing.dateInvoiceCredit.should('have.length', 1);

  //Filtering by payment type
  billing.searchEntries.clear();
  billing.paymentTypeSelect.parent().find('button.dropdown-toggle').click();
  cy.get('.multiselect-container.dropdown-menu')
    .find('label')
    .contains(INVOICE_CREDIT_DETAIL_REPORT_DATA.PAYMENT_TYPE)
    .click();
  reports.updateBtn.click();
  billing.totalAmountInvoiceCredit.should('be.visible');
  billing.totalAmountInvoiceCredit.should(
    'include.text',
    INVOICE_CREDIT_DETAIL_REPORT_DATA.TOTAL_INVOICE_CREDIT_AMOUNT_FIRST
  );
  billing.dateInvoiceCredit.should('have.length', 2);
};

const checkExportCSV = () => {
  reports.exportBtn.click();
  cy.readFile('cypress/downloads/export.csv').should('exist');
};
