import { convertPriceToNumber, getFormattedDatesForActivities } from 'cypress/e2e/utils';
import admin from 'cypress/pages/admin';
import billing from 'cypress/pages/billing';
import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import relationships from 'cypress/pages/relationships';
import reports from 'cypress/pages/reports';
import search from 'cypress/pages/search';
import {
  BASE_URLS,
  CASH_INVOICE,
  CHILD_DATA_PAST_DUE,
  LEDGER_DETAIL_EXPORT_ERRORS,
  TRANSACTION_ACTIONS,
  NEW_PARENT_DATA,
  VOIDED_CREDIT,
  MANAGE_BANK_DEPOSITS_VALUES,
  LEDGER_DETAIL_EXPECTATIONS
} from 'cypress/support/constants';

context('Ledger Detail Report', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('ledger report', () => {
    cy.login(BASE_URLS.LEDGER_DETAIL_REPORT);
    cy.selectDate('start-date-input', '10/01/2023');
    cy.selectDate('end-date-input', '10/03/2023');
    cy.updateReport();

    checkReport();
    checkDebitCreditAmountsWithDetails();
    checkReportWithDetails();
    checkNoEntriesByTimePeriod();
    checkGroupLedgerCode();
    checkCashFilters();
    checkVoidCredits();
    checkExportOptions();
  });

  it('Check manual deposits and adjustment payments on ledger report', () => {
    addFamily();
    addManualPayments();
    checkManageBankDeposits();
    checkLedgerDetail();
    checkEditionDepositsAndLedgerDetail();
    checkAdjustmentPayments();
  });
});

const checkReport = () => {
  let totalDebitBalance = 0;
  let totalCreditBalance = 0;

  reports.ledgerReportTable.should('be.visible');

  // Nest all assertions due to asyncronous nature of cypress. Othwerwise, the total balance will be calculated before the debit and credit balances are calculated.
  reports.creditAmountNoDetail.each(($el, index, list) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        totalCreditBalance += value;

        if (index === list.length - 1) {
          reports.debitAmountNoDetail.each(($el, index, list) => {
            cy.wrap($el)
              .invoke('text')
              .then((text) => {
                const value = convertPriceToNumber(text);
                totalDebitBalance += value;

                if (index === list.length - 1) {
                  cy.checkTotalTag(totalDebitBalance, 'total-debit-balance');
                  cy.checkTotalTag(totalCreditBalance, 'total-credit-balance');
                }
              });
          });
        }
      });
  });
};

const checkDebitCreditAmountsWithDetails = () => {
  let totalCreditBalance = 0;
  reports.detailsCheckbox.check();
  reports.debitCreditAmountCheckbox.check();
  cy.updateReport();

  reports.creditAmount.each(($el, index, list) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        totalCreditBalance += value;

        if (index === list.length - 1) {
          cy.checkTotalTag(totalCreditBalance, 'total-debit-balance');
          cy.checkTotalTag(totalCreditBalance, 'total-credit-balance');
        }
      });
  });
};

const checkReportWithDetails = () => {
  let totalDebitBalance = 0;
  let totalCreditBalance = 0;

  reports.debitCreditAmountCheckbox.uncheck();
  cy.updateReport();

  reports.creditAmount.each(($el, index, list) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        totalCreditBalance += value;

        if (index === list.length - 1) {
          reports.debitAmount.each(($el, index, list) => {
            cy.wrap($el)
              .invoke('text')
              .then((text) => {
                const value = convertPriceToNumber(text);
                totalDebitBalance += value;

                if (index === list.length - 1) {
                  const totalBalance = totalDebitBalance + totalCreditBalance; // This is true if details checkbox = true
                  cy.checkTotalTag(totalBalance, 'total-debit-balance');
                  cy.checkTotalTag(totalBalance, 'total-credit-balance');
                }
              });
          });
        }
      });
  });
};

const checkNoEntriesByTimePeriod = () => {
  cy.selectDate('start-date-input', '10/01/2023');
  cy.selectDate('end-date-input', '10/01/2023');
  cy.updateReport();

  reports.ledgerReportTable.should('be.visible');
  cy.checkTotalTag(0, 'total-debit-balance');
  cy.checkTotalTag(0, 'total-credit-balance');
};

const checkGroupLedgerCode = () => {
  let totalDebitBalance = 0;
  let totalCreditBalance = 0;

  cy.selectDate('end-date-input', '10/03/2023');
  reports.ledgerReportTitle.click();

  reports.debitCreditAmountCheckbox.check();
  reports.sortLedgerCheckbox.check();

  cy.updateReport();

  reports.ledgerSummaryCreditEntry.each(($el, index, list) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        totalCreditBalance += value;

        if (index === list.length - 1) {
          reports.ledgerSummaryDebitEntry.each(($el, index, list) => {
            cy.wrap($el)
              .invoke('text')
              .then((text) => {
                const value = convertPriceToNumber(text);
                totalDebitBalance += value;

                if (index === list.length - 1) {
                  cy.checkTotalTag(totalDebitBalance, 'total-ledger-summary-debit');
                  cy.checkTotalTag(totalCreditBalance, 'total-ledger-summary-credit');
                  cy.checkTotalTag(totalDebitBalance, 'total-debit-balance');
                  cy.checkTotalTag(totalCreditBalance, 'total-credit-balance');
                }
              });
          });
        }
      });
  });
};

const checkCashFilters = () => {
  // Select date with cash entries
  cy.selectDate('start-date-input', '10/10/2023');
  cy.selectDate('end-date-input', '10/10/2023');
  reports.debitCreditAmountCheckbox.check();
  reports.sortLedgerCheckbox.check();
  cy.selectFromDropdown('cash-filter', 0);
  cy.updateReport();

  const totalCashInvoice = CASH_INVOICE.AMOUNT + CASH_INVOICE.CASH_DISCOUNT;

  // None
  cy.checkTotalTag(totalCashInvoice, 'total-debit-balance');
  cy.checkTotalTag(totalCashInvoice, 'total-credit-balance');

  // Cash filter
  cy.selectFromDropdown('cash-filter', 1);
  cy.updateReport();
  cy.checkTotalTag(CASH_INVOICE.CASH_DISCOUNT, 'total-debit-balance');
  cy.checkTotalTag(0, 'total-credit-balance');
  cy.checkTotalTag(CASH_INVOICE.CASH_DISCOUNT, 'total-ledger-summary-debit');
  cy.checkTotalTag(0, 'total-ledger-summary-credit');

  // No cash filter
  cy.selectFromDropdown('cash-filter', 2);
  cy.updateReport();
  cy.checkTotalTag(CASH_INVOICE.AMOUNT, 'total-debit-balance');
  cy.checkTotalTag(CASH_INVOICE.AMOUNT, 'total-credit-balance');
  cy.checkTotalTag(CASH_INVOICE.AMOUNT, 'total-ledger-summary-debit');
  cy.checkTotalTag(CASH_INVOICE.AMOUNT, 'total-ledger-summary-credit');

  //No entries for cash filters
  cy.selectDate('start-date-input', '10/01/2023');
  cy.selectDate('end-date-input', '10/03/2023');
  cy.selectFromDropdown('cash-filter', 1);
  cy.updateReport();
  reports.ledgerReportTable.should('be.visible');
  cy.checkTotalTag(0, 'total-debit-balance');
  cy.checkTotalTag(0, 'total-credit-balance');
};

const checkVoidCredits = () => {
  cy.selectDate('start-date-input', VOIDED_CREDIT.DATE);
  cy.selectDate('end-date-input', VOIDED_CREDIT.DATE);
  reports.ledgerReportTitle.click();
  reports.debitCreditAmountCheckbox.check();
  reports.groupLedgerCodeCheckbox.check();
  reports.detailsCheckbox.check();
  reports.sortLedgerCheckbox.uncheck();
  cy.selectFromDropdown('cash-filter', 0);

  cy.updateReport();
  reports.ledgerCodeEntry.eq(0).click();
  cy.checkTotalTag(VOIDED_CREDIT.AMOUNT, 'total-debit-balance');
  cy.checkTotalTag(VOIDED_CREDIT.AMOUNT, 'total-credit-balance');

  reports.detailDebitAmount.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.equal(VOIDED_CREDIT.AMOUNT);
      });
  });

  reports.detailCreditAmount.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.equal(VOIDED_CREDIT.AMOUNT);
      });
  });

  reports.ledgerCodeEntry.eq(1).click();

  reports.debitAmount.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.equal(VOIDED_CREDIT.AMOUNT);
      });
  });

  reports.creditAmount.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.equal(VOIDED_CREDIT.AMOUNT);
      });
  });

  reports.debitAmountNoDetail.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.not.equal(undefined);
      });
  });

  reports.creditAmountNoDetail.each(($el) => {
    cy.wrap($el)
      .invoke('text')
      .then((text) => {
        const value = convertPriceToNumber(text);
        expect(value).to.not.equal(undefined);
      });
  });
};

const checkExportOptions = () => {
  //Check csv report
  reports.exportBtn.click();
  reports.exportToCsv.click();
  cy.readFile('cypress/downloads/ledger-details.csv').should('exist');

  //Check sage report
  reports.exportBtn.click();
  reports.exportToSage.click();

  cy.task('listDownloads', 'cypress/downloads').then((files) => {
    const fileList = files as string[];

    const matchingFile = fileList.find((file) => file.startsWith('MPLDGRDTL') && file.endsWith('.csv'));
    expect(matchingFile).to.exist;

    if (matchingFile) {
      cy.readFile(`cypress/downloads/${matchingFile}`).should('exist');
    }
  });

  //Check sage (GL import format) report
  reports.exportBtn.click();
  reports.exportToSageGl.click();
  cy.contains(LEDGER_DETAIL_EXPORT_ERRORS.EXPORT_TO_SAGE_GL);
  cy.containsOkAndClick();

  cy.selectFromDropdown('cash-filter', 2);
  cy.updateReport();
  reports.exportBtn.click();
  reports.exportToSageGl.click();
  cy.containsOkAndClick();
  cy.readFile('cypress/downloads/export-glfile.csv').should('exist');

  //Check sage (cash import format) report
  reports.exportBtn.click();
  reports.exportToSageCash.click();
  cy.contains(LEDGER_DETAIL_EXPORT_ERRORS.EXPORT_TO_SAGE_CASH);
  cy.containsOkAndClick();

  cy.selectFromDropdown('cash-filter', 1);
  reports.groupLedgerCodeCheckbox.uncheck();
  reports.debitCreditAmountCheckbox.uncheck();
  cy.updateReport();
  reports.exportBtn.click();
  reports.exportToSageCash.click();
  cy.containsOkAndClick();
  cy.readFile('cypress/downloads/export-sagecashfile.csv').should('exist');
};

const addFamily = () => {
  cy.login(BASE_URLS.DEFAULT);
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA_PAST_DUE.FULL_NAME);
  search.searchManageButton.first().click();

  relationships.relationshipHeaderNavigation.click();
  people.newRelationship.click();
  cy.contains('Create New').click();
  relationships.firstNameInput.type(NEW_PARENT_DATA.FIRST_NAME);
  relationships.lastNameInput.type(NEW_PARENT_DATA.LAST_NAME);
  cy.get('.multiselect-selected-text').click({ multiple: true, force: true });
  cy.get('.form-check-label').contains('Family').click({ force: true });
  relationships.relationshipSaveBtn.click();
  cy.reload();
  navigation.loadingSpinner.should('not.exist');
};

const addManualPayments = () => {
  people.transactionsHeaderNav.click();
  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type(TRANSACTION_ACTIONS.DATE_RANGE);
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();

  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT);
  people.transactionsActionsBtn.click();
  people.creditBtn.click();
  billing.creditAmountInput.clear().type(TRANSACTION_ACTIONS.CREDIT_AMOUNT_FIRST_PAYMENT);
  billing.creditReasonSelect.select(TRANSACTION_ACTIONS.CREDIT_REASON);
  admin.saveEdits.click();
  cy.contains('OK').click();
  admin.saveEdits.should('not.exist');

  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type('01/01/2019 - 01/31/2020');
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();
  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT_AFTER_FIRST_PAYMENT);

  // Add second payment
  people.transactionsActionsBtn.click();
  people.creditBtn.click();
  billing.creditAmountInput.clear().type(TRANSACTION_ACTIONS.CREDIT_AMOUNT_SECOND_PAYMENT);
  billing.creditReasonSelect.select(TRANSACTION_ACTIONS.CREDIT_REASON);
  admin.saveEdits.click();
  cy.contains('OK').click();
  admin.saveEdits.should('not.exist');
  cy.wait(500);
  people.invoiceDateRangeInput.clear();
  people.invoiceDateRangeInput.type('01/01/2020 - 01/31/2020');
  cy.get('button.applyBtn.btn-primary').contains('Apply').click();
  people.invoiceNumberAmount.should('be.visible');
  people.invoiceNumberAmount.should('contain.text', TRANSACTION_ACTIONS.INVOICE_NUMBER_AMOUNT_AFTER_SECOND_PAYMENT);
};

const checkManageBankDeposits = () => {
  cy.visit(BASE_URLS.MANAGE_DEPOSITS_REPORT);
  navigation.loadingSpinner.should('not.exist');

  billing.depositsReportPaymentTypes.should('have.length', 2);
  billing.depositsReportPaymentTypes.should('include.text', MANAGE_BANK_DEPOSITS_VALUES.PAYMENT_TYPE);
  billing.depositCheckbox.click({ multiple: true, force: true });

  billing.depositsReportTotalAmount.should('include.text', MANAGE_BANK_DEPOSITS_VALUES.TOTAL_AMOUNT);
  billing.updateCreateDepositBtn.click();
  admin.saveEdits.click();
  billing.depositsReportPaymentTypes.should('not.exist');
};

const dates = getFormattedDatesForActivities();
const today = dates.today;

const verifyLedgerTable = (expectations) => {
  const { debit, credit, totalDebit, totalCredit } = expectations;

  // Wait for table to be ready
  cy.get('tr.sub-account-group', { timeout: 10000 }).should('exist');

  cy.get('tr.sub-account-group')
    .filter((index, el) => {
      const style = el.getAttribute('style') || '';
      return !style.includes('background-color:#ccc') && !style.includes('font-weight:bold');
    })
    .each((row, index) => {
      const expectedDebit = debit[index];
      const expectedCredit = credit[index];

      // Debit check with normalized text
      if (expectedDebit) {
        cy.wrap(row)
          .find('[data-cy="debit-amount"], [data-cy="detail-debit-amount"]')
          .should('have.length', 1)
          .invoke('text')
          .then((text) => {
            const normalizedText = text.replace(/\s+/g, ' ').trim();
            expect(normalizedText).to.equal(expectedDebit);
          });
      } else {
        cy.wrap(row).find('[data-cy="debit-amount"]').should('not.exist');
      }

      // Credit check with normalized text
      if (expectedCredit) {
        cy.wrap(row)
          .find('[data-cy="credit-amount"], [data-cy="detail-credit-amount"]')
          .should('have.length', 1)
          .invoke('text')
          .then((text) => {
            const normalizedText = text.replace(/\s+/g, ' ').trim();
            expect(normalizedText).to.equal(expectedCredit);
          });
      } else {
        cy.wrap(row).find('[data-cy="credit-amount"]').should('not.exist');
      }
    });

  // Verify totals with normalized text
  reports.totalCreditBalance.invoke('text').then((text) => {
    const normalizedText = text.replace(/\s+/g, ' ').trim();
    expect(normalizedText).to.include(totalCredit);
  });

  reports.totalDebitBalance.invoke('text').then((text) => {
    const normalizedText = text.replace(/\s+/g, ' ').trim();
    expect(normalizedText).to.include(totalDebit);
  });
};

const checkLedgerDetail = () => {
  cy.visit(BASE_URLS.LEDGER_DETAIL_REPORT);
  navigation.loadingSpinner.should('not.exist');
  reports.endDateInput.clear().type(today);
  reports.detailsCheckbox.check();
  reports.updateBtn.click();
  cy.wait(1000);

  verifyLedgerTable(LEDGER_DETAIL_EXPECTATIONS.DEFAULT);
};

const checkEditionDepositsAndLedgerDetail = () => {
  cy.visit(BASE_URLS.MANAGE_DEPOSITS_REPORT);
  navigation.loadingSpinner.should('not.exist');

  billing.pastDepositsBtn.click();
  billing.pastDepositsActionColumn.click();
  billing.depositCheckbox.first().uncheck({ force: true });
  billing.depositsReportTotalAmount.should('include.text', MANAGE_BANK_DEPOSITS_VALUES.TOTAL_AMOUNT_EDITED);
  billing.updateCreateDepositBtn.click();
  admin.saveEdits.click();
  admin.saveEdits.should('not.exist');
  cy.wait(500);
  billing.depositsReportPaymentTypes.should('have.length', 1);

  cy.visit(BASE_URLS.LEDGER_DETAIL_REPORT);
  navigation.loadingSpinner.should('not.exist');
  reports.endDateInput.clear().type(today);
  reports.detailsCheckbox.check();
  reports.updateBtn.click();
  cy.wait(1000);

  verifyLedgerTable(LEDGER_DETAIL_EXPECTATIONS.EDITED);
};

const checkAdjustmentPayments = () => {
  cy.visit(BASE_URLS.INVOICE_4550);
  navigation.loadingSpinner.should('not.exist');
  billing.adjustLineItemBtn.first().click();
  billing.adjustPaymentSaveBtn.click();

  cy.visit(BASE_URLS.LEDGER_DETAIL_REPORT);
  navigation.loadingSpinner.should('not.exist');
  reports.endDateInput.clear().type(today);
  reports.detailsCheckbox.check();
  reports.updateBtn.click();
  cy.wait(1000);

  verifyLedgerTable(LEDGER_DETAIL_EXPECTATIONS.ADJUSTMENT_PAYMENTS);
};
