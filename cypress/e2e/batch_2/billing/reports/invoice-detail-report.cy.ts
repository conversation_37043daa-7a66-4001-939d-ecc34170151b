import { parseAmount, testSort } from 'cypress/e2e/utils';
import admin from 'cypress/pages/admin';
import billing from 'cypress/pages/billing';
import navigation from 'cypress/pages/navigation';
import reports from 'cypress/pages/reports';
import {
  BASE_URLS,
  CHILD_DATA_ARRIVED,
  INVOICE_DETAIL_DATA,
  INVOICE_DETAIL_REPORT_DATA,
  INVOICE_LEDGER_TABLE,
  PAYMENT_STATUS_LABEL
} from 'cypress/support/constants';

context('Invoice Details Report Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.INVOICE_DETAIL_REPORT);
  });

  it('Should check invoice detail report creation and functionalities', () => {
    checkInvoiceDetailTable();
    checkInvoiceDetailActions();
    checkExportCSV();
  });
});

const checkInvoiceDetailTable = () => {
  reports.updateBtn.click();
  billing.invoiceDescription.should('not.exist');
  reports.exportBtn.should('not.exist');

  //Filtering by Date
  reports.startDateInput.clear().type(INVOICE_DETAIL_REPORT_DATA.START_DATE_FIRST);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 2);

  //Filter by name
  billing.searchByName.clear().type(CHILD_DATA_ARRIVED.FIRST_NAME);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 1);

  billing.searchByName.clear();
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 2);

  reports.startDateInput.clear().type(INVOICE_DETAIL_REPORT_DATA.START_DATE_SECOND);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 7);

  //Filtering by Payment Status
  billing.familyPaymentStatus.select(PAYMENT_STATUS_LABEL.PAST_DUE);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 7);

  billing.familyPaymentStatus.select(PAYMENT_STATUS_LABEL.ALL);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 8);

  billing.familyPaymentStatus.select(PAYMENT_STATUS_LABEL.OPEN);
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 7);

  //Check table data
  cy.get('#dvData tbody tr').each(($row, index) => {
    if (index === 0) return;
    const invoice = INVOICE_DETAIL_DATA[index - 1];
    cy.wrap($row).within(() => {
      billing.invoiceNumber.invoke('text').should('contain', invoice.number);
      billing.invoiceDate.should('have.text', invoice.date);
      billing.invoiceDescription.invoke('text').then((text) => {
        expect(text).to.include('Type:');
        expect(text).to.include(invoice.description);

        if (invoice.covers) {
          expect(text).to.include('Covers:');
          expect(text).to.include(invoice.covers);
        }
      });
      billing.invoiceAmount.invoke('text').then((text) => {
        expect(text).to.include(invoice.amount);
        expect(text).to.include('Status:');
        expect(text).to.include(invoice.status);
      });
      billing.invoiceBalance.invoke('text').then((text) => {
        expect(text).to.include(`Family: ${invoice.balance.family}`);
        if (invoice.balance.ccdf) {
          expect(text).to.include(invoice.balance.ccdf);
        }
      });
    });
  });

  //Check sorting options
  testSort(INVOICE_DETAIL_DATA, 'invoiceNumberAsc', 'invoice-number', (i) => parseInt(i.number, 10), 'asc');
  testSort(INVOICE_DETAIL_DATA, 'amountDesc', 'invoice-amount', (i) => parseAmount(i.amount), 'desc');

  //Filter by payer
  billing.familyPaymentStatus.select('All');
  billing.filterByPayer.select('CCDFDESCRIPTION');
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 2);

  billing.filterByPayer.select('');
  reports.updateBtn.click();
  billing.invoiceDescription.should('have.length', 8);
};

const checkInvoiceDetailActions = () => {
  //Check Issue Credit Action
  billing.familyPaymentStatus.select(PAYMENT_STATUS_LABEL.OPEN);
  reports.updateBtn.click();
  reports.selectSort.select('amountAsc');
  reports.updateBtn.click();
  navigation.selectInvoiceAction.first().select('Issue Credit');
  cy.wait(1000);
  cy.get('.modal-title').contains('Credit Invoice').should('exist');
  billing.creditAmountInput.clear().type('10');
  billing.creditReasonSelect.select(INVOICE_DETAIL_REPORT_DATA.PAYMENT_TYPE);
  admin.saveEdits.click();
  admin.saveEdits.should('not.be.visible');
  cy.containsOkAndClick();

  //Check Invoice Ledger Action
  navigation.selectInvoiceAction.first().select('Invoice Ledger');
  billing.invoiceDateLabel.parent().invoke('text').should('include', INVOICE_DETAIL_DATA[0].number);

  cy.get('table.invoice-ledger-table tr')
    .not(':first')
    .each(($row, index) => {
      const expected = INVOICE_LEDGER_TABLE[index];

      cy.wrap($row).within(() => {
        billing.invoiceDescriptionLedger.should('contain.text', expected.description);
        billing.invoiceLedgerDebit.should('have.text', expected.debit);
        billing.invoiceLedgerCredit.should('have.text', expected.credit);
        billing.invoiceLedgerBalance.should('have.text', expected.balance);
      });
    });
  reports.closeBtn.click();
};

const checkExportCSV = () => {
  reports.exportBtn.click({ force: true });
  cy.readFile('cypress/downloads/invoice-details.csv').should('exist');
};
