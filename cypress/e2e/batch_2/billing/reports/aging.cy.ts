import { checkArrayItems, getTextAndConvert } from 'cypress/e2e/utils';
import navigation from 'cypress/pages/navigation';
import { AGING_DATA, AGING_FAMILY_RANGE, AGING_GROUP_BY_OPTIONS, BASE_URLS, LOGIN_DATA } from 'cypress/support/constants';

context('Aging reports', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.AGING);
    navigation.loadingSpinner.should('not.exist');
  });

  it('check calendar filtering, selected range filtering, grouped by, range mapping, total amounts, csv download', () => {
    cy.get('[data-cy="report-orgs-field"]').should('be.visible').click();
    cy.get('.multiselect-all').should('exist').find('.form-check-label').should('contain.text', 'Select all');
    cy.get('.multiselect-option').find('.form-check-label').contains(LOGIN_DATA.ORGNAME2).should('exist');
    checkTotalAmountByAllRanges();
    checkAllColumnItems();
    checkRangeMapping();
    checkGroupByOptions();
    checkWithoutIncludeWaitlist();
  });
});

const checkWithoutIncludeWaitlist = () => {
  cy.get('#chkIncludeWaitList').uncheck({ force: true }).should('not.be.checked');
  setFilters({ range: AGING_FAMILY_RANGE.FIRST_RANGE });
  checkTotalAmountBySelectedRange('total-first-range');
};
const checkTotalAmount = () => {
  getTextAndConvert('total-original-amount').then((text) => {
    const totalOriginalAmount = text;

    getTextAndConvert('total-current-amount').then((text) => {
      const totalCurrentAmount = text;

      getTextAndConvert('total-first-range').then((text) => {
        const totalFirstRange = text;

        getTextAndConvert('total-second-range').then((text) => {
          const totalSecondRange = text;

          getTextAndConvert('total-third-range').then((text) => {
            const totalThirdRange = text;

            getTextAndConvert('total-fourth-range').then((text) => {
              const totalFourthRange = text;

              getTextAndConvert('total-fifth-range').then((text) => {
                const totalFifthRange = text;

                const totalOriginalCalculated =
                  totalCurrentAmount +
                  totalFirstRange +
                  totalSecondRange +
                  totalThirdRange +
                  totalFourthRange +
                  totalFifthRange;

                expect(totalOriginalAmount).to.equal(totalOriginalCalculated);
              });
            });
          });
        });
      });
    });
  });
};

const checkGroupByOptions = () => {
  setFilters({});
  checkTotalAmount();

  setFilters({ groupBy: AGING_GROUP_BY_OPTIONS.INDIVIDUAL });
  checkTotalAmount();
};

const checkTotalAmountBySelectedRange = (rangeTag: string, emptyTable = false) => {
  if (emptyTable) {
    cy.get('[data-cy=aging-report-table]').should('not.exist');
    return;
  }

  getTextAndConvert('total-original-amount').then((total) => {
    const totalOriginalAmount = total;

    getTextAndConvert(rangeTag).then((text) => {
      const rangeAmount = text;
      expect(totalOriginalAmount).to.equal(rangeAmount);
    });
  });
};

const checkTotalAmountByAllRanges = () => {
  setFilters({ range: AGING_FAMILY_RANGE.FIRST_RANGE });
  checkTotalAmountBySelectedRange('total-first-range');

  setFilters({ range: AGING_FAMILY_RANGE.SECOND_RANGE });
  checkTotalAmountBySelectedRange('total-second-range');

  setFilters({ range: AGING_FAMILY_RANGE.THIRD_RANGE });
  checkTotalAmountBySelectedRange('total-third-range', true);

  setFilters({ range: AGING_FAMILY_RANGE.FOURTH_RANGE });
  checkTotalAmountBySelectedRange('total-fourth-range', true);

  setFilters({ range: AGING_FAMILY_RANGE.FIFTH_RANGE });
  checkTotalAmountBySelectedRange('total-fifth-range');
};

const checkRangeMapping = () => {
  setFilters({});
  getTextAndConvert('total-first-range').then((text) => {
    const totalFirstRange = text;
    expect(totalFirstRange).to.equal(RANGE_AMOUNT_MAP[AGING_FAMILY_RANGE.FIRST_RANGE]);

    getTextAndConvert('total-second-range').then((text) => {
      const totalSecondRange = text;
      expect(totalSecondRange).to.equal(RANGE_AMOUNT_MAP[AGING_FAMILY_RANGE.SECOND_RANGE]);

      getTextAndConvert('total-third-range').then((text) => {
        const totalThirdRange = text;
        expect(totalThirdRange).to.equal(0);

        getTextAndConvert('total-fourth-range').then((text) => {
          const totalFourthRange = text;
          expect(totalFourthRange).to.equal(0);

          getTextAndConvert('total-fifth-range').then((text) => {
            const totalFifthRange = text;
            expect(totalFifthRange).to.equal(RANGE_AMOUNT_MAP[AGING_FAMILY_RANGE.FIFTH_RANGE]);

            cy.get('[data-cy=export-csv-btn]').click();
            cy.readFile('cypress/downloads/export.csv').should('exist');
          });
        });
      });
    });
  });
};

const checkColumnItems = (columnItemTag: string, totalColumnTag) => {
  return checkArrayItems(columnItemTag, totalColumnTag);
};

const checkAllColumnItems = () => {
  setFilters({ groupBy: AGING_GROUP_BY_OPTIONS.FAMILY });
  checkColumnItems('original-amount-item', 'total-original-amount');
  checkColumnItems('open-amount-item', 'total-open-amount');
  checkColumnItems('current-amount-item', 'total-current-amount');
  checkColumnItems('first-range-item', 'total-first-range');
  checkColumnItems('second-range-item', 'total-second-range');
  checkColumnItems('third-range-item', 'total-third-range');
  checkColumnItems('fourth-range-item', 'total-fourth-range');
  checkColumnItems('fifth-range-item', 'total-fifth-range');
};

const setFilters = (filters: { date?: string; range?: string | number; groupBy?: string }) => {
  filters.date = filters?.date || AGING_DATA.CONTROL_DATE;
  filters.range = filters?.range || AGING_FAMILY_RANGE.ALL;
  filters.groupBy = filters?.groupBy || AGING_GROUP_BY_OPTIONS.FAMILY;

  cy.selectDate('calendar', filters.date);
  cy.selectFromDropdown('range-days-filter', filters.range);
  cy.selectFromDropdown('group-by-filter', filters.groupBy);

  cy.updateReport();
};

// Map is precalculated for controlDate. TODO: improve this mapping by using fixtures grouping by range
const RANGE_AMOUNT_MAP = {
  [AGING_FAMILY_RANGE.FIRST_RANGE]: 1245,
  [AGING_FAMILY_RANGE.SECOND_RANGE]: 3160.5,
  [AGING_FAMILY_RANGE.FIFTH_RANGE]: 1300
};
