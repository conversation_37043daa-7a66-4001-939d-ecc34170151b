import { BANK_DEPOSITS_RECONCILIATION_REPORT_DATA, BASE_URLS } from 'cypress/support/constants';
import navigation from 'cypress/pages/navigation';
import billing from 'cypress/pages/billing';
import reports from 'cypress/pages/reports';

context('Bank Deposits Reconciliation Report', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.BANK_DEPOSITS_RECONCILIATION_REPORT);
  });

  it('Should have data on csv file', () => {
    billing.dateRangeInput.clear().type(BANK_DEPOSITS_RECONCILIATION_REPORT_DATA.DATE_RANGE);

    cy.get('button.applyBtn.btn-primary').contains('Apply').click();

    reports.updateBtn.click();
    navigation.loadingSpinner.should('not.exist');

    reports.exportBtn.click();
    cy.readFile('cypress/downloads/bank-deposits.csv').should('exist');
  });
});
