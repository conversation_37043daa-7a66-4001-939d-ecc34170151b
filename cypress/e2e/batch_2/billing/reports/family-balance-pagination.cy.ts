import billing from '../../../../pages/billing';
import { BASE_URLS, ACTIVITIES_PAGINATION, FAMIL_BALANCE } from '../../../../support/constants';
import { getPaginationCount } from 'cypress/e2e/utils';

context('Family Balance Report Page Pagination Tests', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.FAMILY_BALANCE_REPORT);
    cy.wait(300);
  });
  it('should verify pagination controls', () => {
    billing.familyBalanceStartDateInput.clear();
    billing.familyBalanceStartDateInput.type(FAMIL_BALANCE.START_DATE);
    billing.familyBalanceUpdateButton.click();
    billing.familyBalancePaginationCount.should('be.visible');
    validatePagination();
    billing.familyBalancePaginationPreviousButton.click();
    validatePagination();
    checkExportToCsv();
  });
});

const validatePagination = () => {
  billing.familyBalancePaginationCount.then(($el) => {
    const [start, end, total] = getPaginationCount($el.text());

    const isEndGreaterThan200 = !(total < ACTIVITIES_PAGINATION.PAGINATION_TEST_TOTAL_COUNT);
    if (isEndGreaterThan200) {
      const currentPageDifference = end - start;
      expect(currentPageDifference).to.eq(ACTIVITIES_PAGINATION.DIFF_COUNT);
      navigateToNextPageAndValidate();
    }
  });
};

const navigateToNextPageAndValidate = () => {
  billing.familyBalancePaginationNextButton.click();
  billing.familyBalancePaginationCount.then(($el) => {
    const [start, end] = getPaginationCount($el.text());
    expect(end - start).to.eq(ACTIVITIES_PAGINATION.DIFF_COUNT);
  });
};

const checkExportToCsv = () => {
  billing.familyBalanceExportCsv.click();
  cy.readFile('cypress/downloads/export.csv').should('exist');
};
