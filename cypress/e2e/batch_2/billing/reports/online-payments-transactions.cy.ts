import billing from 'cypress/pages/billing';
import { BASE_URLS, OPEN_TRANSACTIONS } from 'cypress/support/constants';

context('Validate Open Transactions Report', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.TRANSACTIONS);
  });

  it('should verify report details are not empty', () => {
    const transactionTypes = OPEN_TRANSACTIONS.TRANSACTION_TYPES;
    billing.transactionStartDateInput.clear().type(OPEN_TRANSACTIONS.START_DATE);
    billing.transactionUpdateButton.click();

    billing.transactionsTypes
      .children()
      .then(($types) => {
        return Cypress._.map($types, ($types) => $types.innerText);
      })
      .should('deep.equal', transactionTypes);

    transactionTypes.forEach(($types) => {
      billing.transactionsTypes.select($types, { timeout: 300 });
      billing.transactionUpdateButton.click();
      billing.transactionCardBody.find('div.row', { timeout: 300 }).then(($rows) => {
        if (!($rows.length !== OPEN_TRANSACTIONS.CARD_BODY_ROWS_COUNT)) {
          billing.transactionsReportTable.should('exist').then(() => {
            billing.transactionsReportTable.children('td').should('not.be.empty');
            billing.transactionExportButton.should('be.visible');
          });
        }
      });
    });
  });
});
