import { getFormattedDate, getFutureDate } from 'cypress/e2e/utils';
import billing from 'cypress/pages/billing';
import people from 'cypress/pages/people';
import registration from 'cypress/pages/registration';
import reports from 'cypress/pages/reports';
import { BASE_URLS, CHILD_DATA, COUPON_DATA_BUNDLE } from 'cypress/support/constants';

context('Upcoming Invoices Report Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('Should check upcoming invoices report', () => {
    addProgramForChild();
    checkUpcomingInvoicesReport();
  });
});

const addProgramForChild = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY, 'Parent');
  people.personName.contains(CHILD_DATA.FIRST_NAME).click();
  people.programsHeaderNavigation.should('be.visible');
  people.programsHeaderNavigation.click();
  registration.plans.eq(4).click();
  people.addProgramsContinue.click();
  cy.wait(1000);
  people.addProgramsContinue.click();

  registration.addDiscountBtn.realHover();
  registration.couponDiscount.click({ force: true });
  people.couponCodeName.type(COUPON_DATA_BUNDLE.CODE);
  people.applyCouponBtn.click();
  cy.containsOkAndClick();
  people.addProgramsSubmit.click({ force: true });
  cy.containsOkAndClick();
};

const checkUpcomingInvoicesReport = () => {
  cy.login(BASE_URLS.UPCOMING_INVOICES_REPORT);

  const endDate = getFutureDate(30);
  const formattedEndDate = getFormattedDate(false, endDate);

  reports.endDateInput.clear().type(formattedEndDate);
  reports.updateBtn.click();

  billing.upcomingInvoiceDate.should('exist');
  billing.upcomingInvoicePersonName.should('include.text', CHILD_DATA.FIRST_NAME);
  billing.upcomingInvoiceDescription.should('include.text', CHILD_DATA.PLAN_DESCRIPTION);
  billing.upcomingInvoiceDueDate.should('exist');
  billing.upcomingInvoiceOpenAmount.should('exist');
};
