import billing from 'cypress/pages/billing';
import admin from 'cypress/pages/admin';
import search from 'cypress/pages/search';
import people from 'cypress/pages/people';
import {
  BASE_URLS,
  PLAN_DATA,
  PLAN_TABLE_DATA,
  ITEM_DATA,
  PUNCH_CARD_DATA,
  COUPON_TABLE_DATA,
  COUPON_DATA,
  COUPON_UTILS,
  SCALED_AMOUNTS,
  CHILD_DATA,
  PLAN_CHILD,
  ITEM_DATA_RECURRING_DAYS
} from 'cypress/support/constants';
import { getFormattedFutureDate } from '../../utils';

context('Billing Admin Plans', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.addCustomization('billing/configuration/punchCards');
    cy.login(BASE_URLS.BILLING_ADMIN_PLANS_URL);
  });

  it("should display the list of plans, edit a 'Weekly - Flat Rate' plan, add selective weeks, set their amounts, and save the changes", () => {
    billing.planFrequency.each(($el) => {
      const text = $el.text();
      if (text.includes(PLAN_DATA.PLAN_FREQUENCY_DATA)) {
        const planId = $el
          .closest('tr')
          .find('.btn.btn-primary.font-weight-bolder.btn-text-white')
          .attr('id')
          .split('-')[1];
        cy.get(`#dropdown-${planId}`).click();

        billing.editPlanById(planId).click({ timeout: 5000 });

        checkDetailEditor();

        cy.get('#dateTypeSelect').select(PLAN_DATA.PLAN_PERIOD);

        cy.get('#timePeriodsSelect')
          .find('option')
          .eq(1)
          .then(function ($option) {
            cy.get('#timePeriodsSelect').select($option.val()).trigger('change');
          });

        return false;
      }
    });

    cy.get('#btnAddSelectiveWeek').click();
    cy.get('#btnAddSelectiveWeek').click();
    clearField('input[data-amount-id="1"]');
    cy.get('input[data-amount-id="1"]').type('800');
    cy.get('#btnAddSelectiveWeek').click();
    cy.get('input[data-amount-id="2"]').should('have.value', '550.00');

    checkInputTakesNumbersOnly('abc');
    checkInputTakesNumbersOnly('$');

    clearField('input[data-amount-id="2"]');
    cy.get('input[data-amount-id="2"]').type('100');

    cy.get('#btnSave').click();
    cy.wait(1000);
    cy.get('#simpleModal').should('not.exist');
  });

  it('Create plan/coupons and check configurations', () => {
    checkAddPlan();
    checkAddItem();
    checkAddBundle();
    checkAddPunchCard();
    checkAddItemRecurringDays();
    checkSearchByName();
    checkTable();
    checkSuspend();
    checkArchive();
    checkPropagate();
    checkShowArchivedPlans();
    checkCouponTable();
    checkAddCoupon();
    checkEditCoupon();
    checkArchiveCoupon();
    checkSuspendCoupon();
    checkShowArchivedCoupons();
    checkExpiredCoupon();
    checkChildBilling();
  });

  it('Check that registrationFlow enabled/disabled hides Program Detail Editor', () => {
    cy.removeCustomization('registrationFlow');
    cy.visit(BASE_URLS.BILLING_ADMIN_PLANS_URL);
    addPlanNoRegistrationFlow();
    addItemNoRegistrationFlow();
    addBundleNoRegistrationFlow();
    addPunchCardNoRegistrationFlow();
  });
});

function checkInputTakesNumbersOnly(text) {
  clearField('input[data-amount-id="2"]');
  cy.get('input[data-amount-id="2"]').type(text);
  cy.get('#btnSave').click();
  cy.contains('Please correct one or more issues with your form before submitting.').should('exist');
  cy.get('body').click(0, 0);
}

function clearField(selector) {
  cy.get(selector).clear();
  cy.get(selector).then(($input) => {
    if ($input.val() !== '') {
      cy.get(selector).type('{selectall}{backspace}');
    }
  });
}

const checkAddPlan = () => {
  billing.addPlanBtn.click();
  checkDetailEditor();
  billing.addPlanDescription.type(PLAN_DATA.PLAN_NAME);
  billing.addPlanCategory.select(PLAN_DATA.PLAN_CATEGORY);

  billing.addPlanProgram.select(PLAN_DATA.PLAN_PROGRAM);
  billing.addPlanFrequency.select(PLAN_DATA.PLAN_FREQUENCY);
  billing.addPlanAmount.clear();
  billing.addPlanAmount.type(PLAN_DATA.PLAN_AMOUNT);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(getFormattedFutureDate(40), { force: true });
  cy.get('td.active.day').click();
  billing.addPlanLedgerAccount.click();
  billing.addPlanLedgerAccount.type(PLAN_DATA.PLAN_LEDGER_ACCOUNT);
  billing.addPlanDateType.select(PLAN_DATA.PLAN_DATE_TYPE);
  billing.addPlanServiceDates
    .find('option')
    .eq(1)
    .then(function ($option) {
      billing.addPlanServiceDates.select($option.val()).trigger('change');
    });

  billing.addPlanStartTime.type(PLAN_DATA.PLAN_START_TIME);
  billing.addPlanEndTime.type(PLAN_DATA.PLAN_END_TIME);
  billing.addPlanRegStartDate.type(PLAN_DATA.PLAN_REG_START_DATE, {
    force: true
  });
  cy.get('td.active.day').click();
  billing.addPlanRegEndDate.type(PLAN_DATA.PLAN_REG_END_DATE, { force: true });
  cy.get('td.active.day').click();
  billing.addPlanGrades.select(PLAN_DATA.PLAN_GRADES, { force: true });
  billing.addPlanScheduleType.select(PLAN_DATA.PLAN_SCHEDULE_TYPE);
  billing.addPlanRegFreeExempt.check({ force: true });

  admin.saveEdits.click({ force: true });
  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', PLAN_DATA.PLAN_NAME).should('be.visible');
  });
};

const checkAddItem = () => {
  billing.addPlanBtn.click();
  billing.checkItem.check({ force: true });
  checkDetailEditor();
  billing.addPlanDescription.type(ITEM_DATA.ITEM_NAME);
  billing.addPlanProgram.select(ITEM_DATA.ITEM_PROGRAM);
  billing.addPlanAmount.clear().type(ITEM_DATA.ITEM_AMOUNT);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(ITEM_DATA.ITEM_EXPIRATION_DATE, {
    force: true
  });
  cy.get('td.active.day').click();
  billing.addPlanLedgerAccount.type(ITEM_DATA.ITEM_LEDGER_ACCOUNT);
  billing.checkRefundable.check({ force: true });
  billing.addPlanDateType.select(ITEM_DATA.ITEM_DATE_TYPE);
  billing.addDaysBtn.click();
  billing.addServiceDatePicker.type(getFormattedFutureDate(40), { force: true });
  billing.addPlanStartTime.type;
  billing.addPlanStartTime.type(ITEM_DATA.ITEM_START_TIME, {force: true});
  billing.addPlanEndTime.type(ITEM_DATA.ITEM_END_TIME);
  billing.addPlanRegStartDate.type(ITEM_DATA.ITEM_REG_START_DATE, {
    force: true
  });
  cy.get('td.active.day').click();
  billing.addPlanRegEndDate.type(ITEM_DATA.ITEM_REG_END_DATE, { force: true });
  cy.get('td.active.day').click();
  billing.addPlanGrades.select(ITEM_DATA.ITEM_GRADES, { force: true });
  billing.addPlanScheduleType.select(ITEM_DATA.ITEM_SCHEDULE_TYPE);
  admin.saveEdits.click({ force: true });

  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', ITEM_DATA.ITEM_NAME).should('be.visible');
  });
};

const checkAddItemRecurringDays = () => {
  const daysToCheck = ['mon'];
  const allDays = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
  const daysNotChecked = allDays.filter((day) => !daysToCheck.includes(day));

  billing.addPlanBtn.click();
  billing.checkItem.check({ force: true });
  checkDetailEditor();
  billing.addPlanDescription.type(ITEM_DATA_RECURRING_DAYS.ITEM_NAME);
  billing.addPlanProgram.select(ITEM_DATA_RECURRING_DAYS.ITEM_PROGRAM);
  billing.addPlanAmount.clear().type(ITEM_DATA_RECURRING_DAYS.ITEM_AMOUNT);
  billing.addPlanLedgerAccount.type(ITEM_DATA_RECURRING_DAYS.ITEM_LEDGER_ACCOUNT);
  billing.addPlanDateType.select(ITEM_DATA_RECURRING_DAYS.ITEM_DATE_TYPE);
  billing.recurringStartDate.type(ITEM_DATA_RECURRING_DAYS.ITEM_REC_START_DATE, { force: true });
  cy.get('td.active.day').click();
  billing.recurringFrequency.type(ITEM_DATA_RECURRING_DAYS.ITEM_RECURRING_FREQUENCY);
  billing.recurringOccurrences.type(ITEM_DATA_RECURRING_DAYS.ITEM_RECURRING_OCCURRENCES);

  if (daysToCheck.length > 0) {
    billing.recurringDays(daysToCheck).check({ force: true });
  }

  admin.saveEdits.click({ force: true });
  cy.wait(1000);

  cy.contains('[data-cy="plan-desc"]', ITEM_DATA_RECURRING_DAYS.ITEM_NAME)
    .closest('tr')
    .find('.btn[data-toggle="dropdown"]:contains("Actions")')
    .click();
  cy.get('.dropdown-menu.show').within(() => {
    cy.contains('Edit').click();
  });

  cy.wait(500);

  if (daysToCheck.length > 0) {
    billing.recurringDays(daysToCheck).should('be.checked');
  }

  if (daysNotChecked.length > 0) {
    billing.recurringDays(daysNotChecked).should('not.be.checked');
  }

  admin.cancelEdits.click({ force: true });
};

const checkAddBundle = () => {
  billing.addPlanBtn.click();
  billing.checkBundle.check({ force: true });
  checkDetailEditor();
  billing.bundlePlanNumber('0').select(3);
  billing.bundlePlanNumber('1').select(2);

  SCALED_AMOUNTS.forEach((amountRow, rowIndex) => {
    amountRow.forEach((value, colIndex) => {
      const inputName = `scaledAmount_${rowIndex}_${colIndex}`;
      cy.get(`input[name="${inputName}"]`).clear().type(value);
    });
  });

  billing.addPlanRegFreeExempt.check({ force: true });
  admin.saveEdits.click({ force: true });
};

const checkSearchByName = () => {
  billing.searchByName.type(PLAN_DATA.PLAN_NAME, { force: true });
  billing.planDesc.should('contain', PLAN_DATA.PLAN_NAME);
  cy.wait(500);
};

const checkAddPunchCard = () => {
  billing.addPlanBtn.click();
  billing.checkPunchCard.check({ force: true });
  checkDetailEditor();
  billing.addPlanDescription.type(PUNCH_CARD_DATA.PUNCH_CARD_NAME);
  billing.addPlanCategory.select(PUNCH_CARD_DATA.PUNCH_CARD_CATEGORY);
  billing.addPlanProgram.select(PUNCH_CARD_DATA.PUNCH_CARD_PROGRAM);
  billing.addPlanAmount.clear().type(PUNCH_CARD_DATA.PUNCH_CARD_AMOUNT);
  billing.addPunchCardNumberOfDays.type(PUNCH_CARD_DATA.PUNCH_CARD_DAYS);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(PUNCH_CARD_DATA.PUNCH_CARD_EXPIRATION_DATE, {
    force: true
  });
  billing.addPlanLedgerAccount.type(PUNCH_CARD_DATA.PUNCH_CARD_LEDGER_ACCOUNT);
  admin.saveEdits.click({ force: true });

  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', PUNCH_CARD_DATA.PUNCH_CARD_NAME).should('be.visible');
  });
};

const checkTable = () => {
  for (let n = 0; n < 5; n++) {
    billing.type.eq(n).should('include.text', PLAN_TABLE_DATA[n].type);
    billing.planDesc
      .eq(n)
      .invoke('text')
      .then((text) => {
        const normalizedText = text.replace(/\n[\t\n]*/g, ' ').trim();
        expect(normalizedText).to.include(PLAN_TABLE_DATA[n].description);
      });
    billing.planFrequency.eq(n).should('include.text', PLAN_TABLE_DATA[n].details);
    billing.planCategory.eq(n).should('include.text', PLAN_TABLE_DATA[n].category);
    billing.planLedgerAccount.eq(n).should('include.text', PLAN_TABLE_DATA[n].ledgerAccount);
    billing.planExpirationDate.eq(n).should('include.text', PLAN_TABLE_DATA[n].expires);
  }
  cy.wait(500);
  billing.searchBtnClear.click();
};

const checkSuspend = () => {
  billing.planDesc.each(($el) => {
    const text = $el.text();
    if (text.includes(PLAN_DATA.PLAN_SUSPEND)) {
      const planId = clickDropdown($el);
      billing.suspensionBtn(planId).click();

      cy.get('#suspend-date-picker').clear().type(getFormattedFutureDate(40), { force: true });

      cy.containsOkAndClick();
      return false;
    }
  });
};

const checkArchive = () => {
  billing.searchByName.type(PLAN_DATA.PLAN_ARCHIVE, { force: true });
  billing.planDesc.each(($el) => {
    const text = $el.text();
    if (text.includes(PLAN_DATA.PLAN_ARCHIVE)) {
      const planId = clickDropdown($el);

      billing.archiveBtn(planId).click();

      cy.get('#archive-unenroll').check().should('be.checked');
      cy.containsOkAndClick();
      cy.containsOkAndClick();

      return false;
    }
  });

  billing.planDesc.should('not.contain', PLAN_DATA.PLAN_ARCHIVE + ' Archived');
};

const checkPropagate = () => {
  billing.planDesc.each(($el) => {
    const text = $el.text();
    if (text.includes(PLAN_DATA.PLAN_PROPAGATE)) {
      const planId = clickDropdown($el);

      billing.propagateBtn(planId).click();
      cy.contains('Yes, Propagate').click();
      cy.containsOkAndClick();
      return false;
    }
  });
};

const checkShowArchivedPlans = () => {
  billing.searchBtnClear.click();
  billing.showArchivedPlans.check({ force: true });

  billing.planDesc.each(($cell) => {
    if ($cell.text().includes('Archived')) {
      cy.wrap($cell).contains('Archived').should('be.visible');
    }
  });
};

const checkCouponTable = () => {
  billing.couponTab.click();
  cy.wait(500);

  for (let n = 0; n < 2; n++) {
    billing.couponCode.eq(n).should('include.text', COUPON_TABLE_DATA[n].couponCode);
    billing.couponDesc.eq(n).within(() => {
      cy.contains(COUPON_TABLE_DATA[n].description);
      cy.contains(COUPON_TABLE_DATA[n].expiredText);
    });
    billing.couponAmount.eq(n).should('include.text', COUPON_TABLE_DATA[n].amount);
    billing.couponStartDate.eq(n).should('include.text', COUPON_TABLE_DATA[n].regStart);
    billing.couponEndDate.eq(n).should('include.text', COUPON_TABLE_DATA[n].regEnd);
  }
};

const checkAddCoupon = () => {
  billing.addCouponBtn.click();
  billing.addCouponCode.type(COUPON_DATA.CODE, { force: true });
  billing.addCouponDesc.type(COUPON_DATA.DESCRIPTION, { force: true });
  billing.addAmountType.select(COUPON_DATA.AMOUNT_TYPE);
  billing.addCouponAmount.clear();
  billing.addCouponAmount.type(COUPON_DATA.AMOUNT);
  billing.addRegEndDate.type(COUPON_DATA.REGISTRATION_END_DATE, {
    force: true
  });
  cy.get('td.active.day').click();
  billing.addCouponExpirationDate.type(COUPON_DATA.EXPIRATION_DATE, {
    force: true
  });
  cy.get('td.active.day').click();

  billing.addMaxNumberRegistration.type(COUPON_DATA.MAX_NUMBER_OF_REGISTRATIONS);
  billing.addRestricBillingPlan.select(COUPON_DATA.RESTRICT_FOR_BILLING_PLANS);

  cy.get('.select2-selection').eq(1).click();
  billing.addCouponBillingPlan.select(COUPON_DATA.BILLING_PLAN, {
    force: true
  });
  billing.addLedgerCode.type(COUPON_DATA.LEDGER_CODE);
  billing.addUseCouponInBundles.select(COUPON_DATA.USE_COUPON_IN_BUNDLES);
  admin.saveEdits.click({ force: true });

  billing.couponTable.should('be.visible').within(() => {
    cy.contains('td', COUPON_DATA.DESCRIPTION).should('be.visible');
  });
};

const checkEditCoupon = () => {
  billing.searchCuponBtnClear.click();
  billing.couponCode.each(($el) => {
    const text = $el.text();
    if (text.includes(COUPON_UTILS.SUSPEND_CODE)) {
      const id = clickDropdown($el);
      billing.editCouponBtn(id).click();

      billing.addCouponDesc.type(COUPON_UTILS.EDIT_DESC, { force: true });

      return false;
    }
  });

  cy.get('#btnSave').click();

  billing.couponTable.should('be.visible').within(() => {
    cy.contains('td', COUPON_UTILS.EDIT_DESC).should('be.visible');
  });
};

const checkSuspendCoupon = () => {
  billing.couponCode.each(($el) => {
    const text = $el.text();
    if (text.includes(COUPON_UTILS.SUSPEND_CODE)) {
      const id = clickDropdown($el);

      billing.suspendCouponBtn(id).click();

      cy.get('#suspend-date-picker').clear().type(getFormattedFutureDate(2), { force: true });

      cy.containsOkAndClick();
      return false;
    }
  });
};

const checkArchiveCoupon = () => {
  billing.couponCode.each(($el) => {
    const text = $el.text();
    if (text.includes(COUPON_UTILS.ARCHIVE_CODE)) {
      const id = clickDropdown($el);
      billing.archiveCouponBtn(id).click();
      cy.containsOkAndClick();
      cy.containsOkAndClick();
      return false;
    }
  });

  billing.couponCode.should('not.contain', COUPON_UTILS.ARCHIVE_CODE);
};

const checkShowArchivedCoupons = () => {
  billing.searchCuponBtnClear.click();
  billing.showArchivedCoupons.check({ force: true });

  billing.couponDesc.each(($cell) => {
    if ($cell.text().includes('Archived')) {
      cy.wrap($cell).contains('Archived').should('be.visible');
    }
  });
};

const checkExpiredCoupon = () => {
  billing.couponDesc.each(($cell) => {
    if ($cell.text().includes('Expired')) {
      cy.wrap($cell).contains('Expired').should('be.visible');
    }
  });
};

const checkChildBilling = () => {
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();
  people.billingTab.click();
  billing.btnAddPlan.click();
  billing.selectPlanName.select(PLAN_CHILD.PLAN_NAME, { force: true });
  billing.overrideRate.type(PLAN_CHILD.OVERRIDE_RATE);
  billing.includeEnrollmentForecastingDate.type(PLAN_CHILD.INCLUDE_FORECASTING_DATE);
  billing.enrollmentForecastEnd.type(PLAN_CHILD.EMROLLMENT_FORECAST_END);
  billing.addAllocationBtn.click();
  billing.allocationType.select(PLAN_CHILD.ALLOCATION_TYPE);
  billing.allocationCouponCode.select(PLAN_CHILD.COUPON);
  billing.applyBtn.click();
  admin.saveEdits.click();
};

function clickDropdown($el: JQuery<HTMLElement>): string {
  const id = $el.closest('tr').find('.btn.btn-primary.font-weight-bolder.btn-text-white').attr('id').split('-')[1];

  cy.get(`#dropdown-${id}`).click();
  return id;
}

const checkDetailEditor = () => {
  // Verif that the editor container exists
  cy.get(`[data-cy="program-details-label"]`).should('be.visible');
  cy.get('.tox-tinymce').should('be.visible');

  // Close notification box for no api key if it exists
  cy.get('.tox-notification__dismiss')
    .its('length')
    .then((length) => {
      if (length > 0) {
        cy.get('.tox-notification__dismiss').click();
      }
    });
};

const checkEditorNotVisible = () => {
  cy.get(`[data-cy="program-details-label"]`).should('not.exist');
  cy.get('.tox-tinymce').should('not.exist');
};

const addPlanNoRegistrationFlow = () => {
  billing.addPlanBtn.click();
  checkEditorNotVisible();
  billing.addPlanDescription.type(PLAN_DATA.PLAN_NAME);
  billing.addPlanCategory.select(PLAN_DATA.PLAN_CATEGORY);

  billing.addPlanProgram.select(PLAN_DATA.PLAN_PROGRAM);
  billing.addPlanFrequency.select(PLAN_DATA.PLAN_FREQUENCY);
  billing.addPlanAmount.clear();
  billing.addPlanAmount.type(PLAN_DATA.PLAN_AMOUNT);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(getFormattedFutureDate(40), { force: true });
  cy.get('td.active.day').click();
  billing.addPlanLedgerAccount.click();
  billing.addPlanLedgerAccount.type(PLAN_DATA.PLAN_LEDGER_ACCOUNT);

  admin.saveEdits.click({ force: true });
  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', PLAN_DATA.PLAN_NAME).should('be.visible');
  });
};

const addItemNoRegistrationFlow = () => {
  billing.addPlanBtn.click();
  billing.checkItem.check({ force: true });
  checkEditorNotVisible();
  billing.addPlanDescription.type(ITEM_DATA.ITEM_NAME);
  billing.addPlanProgram.select(ITEM_DATA.ITEM_PROGRAM);
  billing.addPlanAmount.clear().type(ITEM_DATA.ITEM_AMOUNT);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(ITEM_DATA.ITEM_EXPIRATION_DATE, {
    force: true
  });
  cy.get('td.active.day').click();
  billing.addPlanLedgerAccount.type(ITEM_DATA.ITEM_LEDGER_ACCOUNT);
  admin.saveEdits.click({ force: true });

  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', ITEM_DATA.ITEM_NAME).should('be.visible');
  });
};

const addBundleNoRegistrationFlow = () => {
  billing.addPlanBtn.click();
  billing.checkBundle.check({ force: true });
  checkEditorNotVisible();
  billing.bundlePlanNumber('0').select(3);
  billing.bundlePlanNumber('1').select(2);

  SCALED_AMOUNTS.forEach((amountRow, rowIndex) => {
    amountRow.forEach((value, colIndex) => {
      const inputName = `scaledAmount_${rowIndex}_${colIndex}`;
      cy.get(`input[name="${inputName}"]`).clear().type(value);
    });
  });
  admin.saveEdits.click({ force: true });
};

const addPunchCardNoRegistrationFlow = () => {
  billing.addPlanBtn.click();
  billing.checkPunchCard.check({ force: true });
  checkEditorNotVisible();
  billing.addPlanDescription.type(PUNCH_CARD_DATA.PUNCH_CARD_NAME);
  billing.addPlanCategory.select(PUNCH_CARD_DATA.PUNCH_CARD_CATEGORY);
  billing.addPlanProgram.select(PUNCH_CARD_DATA.PUNCH_CARD_PROGRAM);
  billing.addPlanAmount.clear().type(PUNCH_CARD_DATA.PUNCH_CARD_AMOUNT);
  billing.addPunchCardNumberOfDays.type(PUNCH_CARD_DATA.PUNCH_CARD_DAYS);
  billing.addPlanExpiration.click({ force: true });
  billing.addPlanExpiration.clear();
  billing.addPlanExpiration.type(PUNCH_CARD_DATA.PUNCH_CARD_EXPIRATION_DATE, {
    force: true
  });
  billing.addPlanLedgerAccount.type(PUNCH_CARD_DATA.PUNCH_CARD_LEDGER_ACCOUNT);
  admin.saveEdits.click({ force: true });

  billing.billingTable.should('be.visible').within(() => {
    cy.contains('td', PUNCH_CARD_DATA.PUNCH_CARD_NAME).should('be.visible');
  });
};
