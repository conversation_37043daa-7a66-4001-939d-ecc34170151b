import billing from 'cypress/pages/billing';
import {
  BASE_URLS,
  CHILD_DATA,
  DISCOUNT_DATA,
  DISCOUNTS_LIST,
  DISCOUNTS_TABLE,
  NEW_DISCOUNTS,
  NEW_PAYERS,
  PAYER_DATA,
  PAYERS_LIST,
  PAYERS_TABLE,
  PLAN_CHILD,
  MANAGE_PROGRAMS,
  PLAN_DATA,
  PLAN_SERVICES_DATES,
  PROGRAMS,
  TIME_PERIOD,
  TIME_PERIOD_DATA
} from 'cypress/support/constants';
import billingAdmin from 'cypress/pages/billing-admin';
import search from 'cypress/pages/search';
import admin from 'cypress/pages/admin';
import { subtractAmountsAndFormat } from 'cypress/e2e/utils';
import { getFormattedDateIso, getFutureDate } from '../../utils';
import navigation from 'cypress/pages/navigation';

context('Billing Configuration', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.BILLING_ADMIN);
  });

  it('Verify Discount configuration', () => {
    checkDiscountsConfiguration();
    checkDiscountPlan();
  });

  it('Verify Payer configuration', () => {
    checkPayersConfiguration();
    checkPayerDropdownPlan();
  });

  it('Verify Time period', () => {
    checkTimePeriod();
    checkDropDownTimePeriod();
  });

  it('Verify manage program', () => {
    checkManagePrograms();
    checkDropDownProgram();
  });
});

const checkDiscountsConfiguration = () => {
  navigation.billingAdminConfigurationButton.click();
  navigation.loadingSpinner.should('not.exist');
  billingAdmin.settingsTab.click();
  billingAdmin.manageDiscounts.click();
  billingAdmin.discountData.should('have.length', 9);

  billingAdmin.discountData.each((row, index) => {
    const expected = DISCOUNTS_TABLE[index];
    cy.wrap(row).within(() => {
      billingAdmin.discountCode.should('contain.text', expected.code);
      billingAdmin.discountDescription.should('contain.text', expected.description);
      billingAdmin.discountLedger.should('contain.text', expected.ledger);
      billingAdmin.discountDefault.should('contain.text', expected.default);
    });
  });
  billingAdmin.addNewDiscount.click({ force: true });
  billingAdmin.discountCodeInput.scrollIntoView().should('be.visible');
  billingAdmin.discountCodeInput.type(NEW_DISCOUNTS[0].code);
  billingAdmin.discountDescInput.type(NEW_DISCOUNTS[0].desc);
  billingAdmin.discountLedgerInput.type(NEW_DISCOUNTS[0].ledger);
  billingAdmin.discountAmountInput.type(NEW_DISCOUNTS[0].amount);
  billingAdmin.discountAmountType.select(NEW_DISCOUNTS[0].amountType);
  billingAdmin.saveDiscount.click();
  billingAdmin.discountData.should('have.length', 10);
  billingAdmin.discountCode.last().should('have.text', NEW_DISCOUNTS[0].code);
  cy.wait(500);
  billingAdmin.addNewDiscount.click({ force: true });
  billingAdmin.discountCodeInput.scrollIntoView().should('be.visible');
  billingAdmin.discountCodeInput.type(NEW_DISCOUNTS[1].code);
  billingAdmin.discountDescInput.type(NEW_DISCOUNTS[1].desc);
  billingAdmin.discountLedgerInput.type(NEW_DISCOUNTS[1].ledger);
  billingAdmin.discountAmountInput.type(NEW_DISCOUNTS[1].amount);
  billingAdmin.discountAmountType.select(NEW_DISCOUNTS[1].amountType);
  billingAdmin.saveDiscount.click();
  billingAdmin.discountData.should('have.length', 11);
  billingAdmin.discountCode.last().should('have.text', NEW_DISCOUNTS[1].code);

  billingAdmin.discountData
    .contains(DISCOUNT_DATA.DISCOUNT)
    .parents('tr')
    .within(() => {
      billingAdmin.editDiscount.click();
    });
  billingAdmin
    .discountEditRow(DISCOUNT_DATA.DISCOUNT)
    .should('be.visible')
    .within(() => {
      billingAdmin.editDiscountAmount.clear().type(DISCOUNT_DATA.EDITED_AMOUNT);
      billingAdmin.saveEditDiscount.click();
    });
  billingAdmin.discountData
    .contains(DISCOUNT_DATA.DISCOUNT)
    .parent()
    .within(() => {
      billingAdmin.discountDefault.should('have.text', DISCOUNT_DATA.EDITED_FORMATTED_AMOUNT);
    });

  billingAdmin.discountData
    .contains(DISCOUNT_DATA.ARCHIVED_DISCOUNT)
    .parents('tr')
    .within(() => {
      billingAdmin.archivedDiscount.click();
    });
  cy.wait(500);
  cy.get('.swal2-confirm').click({ force: true });
  cy.wait(500);
  billingAdmin.discountArchived.eq(1).should('have.text', DISCOUNT_DATA.ARCHIVED_TEXT);
};

const checkDiscountPlan = () => {
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();
  cy.wait(500);
  billing.billingTabParents.click();
  billing.btnAddPlan.click();
  billing.selectPlanName.select(DISCOUNT_DATA.PLAN_NAME, { force: true });
  billing.includeEnrollmentForecastingDate.type(PLAN_CHILD.INCLUDE_FORECASTING_DATE);
  billing.addAllocationBtn.click();
  billing.allocationType.select(DISCOUNT_DATA.ALLOCATION_TYPE);
  billing.allocationDiscountType.find('option').each(($option, index) => {
    cy.wrap($option)
      .invoke('text')
      .then((actualText) => {
        expect(actualText.trim()).to.equal(DISCOUNTS_LIST[index]);
      });
  });
  billing.allocationDiscountType.select('multipleFamily');
  billing.applyBtn.click();
  billing.familyAmount.should(
    'have.text',
    subtractAmountsAndFormat(DISCOUNT_DATA.PLAN_AMOUNT, DISCOUNT_DATA.EDITED_AMOUNT)
  );
  admin.saveEdits.click();
};

const checkPayersConfiguration = () => {
  navigation.billingAdminConfigurationButton.click();
  navigation.loadingSpinner.should('not.exist');
  billingAdmin.settingsTab.click();
  billingAdmin.managePayers.click();
  billingAdmin.payerData.should('have.length', 4);

  billingAdmin.payerData.each((row, index) => {
    const expected = PAYERS_TABLE[index];
    cy.wrap(row).within(() => {
      billingAdmin.payerCode.should('contain.text', expected.code);
      billingAdmin.payerDescription.should('contain.text', expected.description);
      billingAdmin.payerLedgerAccount.should('contain.text', expected.ledgerAccount);
      billingAdmin.payerCashLedgerAccount.should('contain.text', expected.cashLedgerAccount);
    });
  });
  billingAdmin.addNewPayer.click({ force: true });
  billingAdmin.payerCodeInput.scrollIntoView().should('be.visible');
  billingAdmin.payerCodeInput.type(NEW_PAYERS[0].code);
  billingAdmin.payerDescInput.type(NEW_PAYERS[0].desc);
  billingAdmin.payerLedgerInput.type(NEW_PAYERS[0].ledger);
  billingAdmin.payerCashLedgerInput.type(NEW_PAYERS[0].cashLedger);
  billingAdmin.savePayer.click();
  billingAdmin.payerData.should('have.length', 5);
  billingAdmin.payerCode.last().should('have.text', NEW_PAYERS[0].code);
  cy.wait(500);
  billingAdmin.addNewPayer.click({ force: true });
  billingAdmin.payerCodeInput.scrollIntoView().should('be.visible');
  billingAdmin.payerCodeInput.type(NEW_PAYERS[1].code);
  billingAdmin.payerDescInput.type(NEW_PAYERS[1].desc);
  billingAdmin.payerLedgerInput.type(NEW_PAYERS[1].ledger);
  billingAdmin.savePayer.click();
  billingAdmin.payerData.should('have.length', 6);
  billingAdmin.payerCode.last().should('have.text', NEW_PAYERS[1].code);

  billingAdmin.payerData
    .contains(PAYER_DATA.PAYER)
    .parents('tr')
    .within(() => {
      billingAdmin.editPayerBtn.click();
    });
  billingAdmin
    .payerEditRow(PAYER_DATA.PAYER)
    .should('be.visible')
    .within(() => {
      billingAdmin.editPayerDesc.clear().type(PAYER_DATA.EDITED_DESC);
      billingAdmin.saveEditPayer.click();
    });
  billingAdmin.payerDescription.eq(2).should('have.text', PAYER_DATA.EDITED_DESC);
  billingAdmin.payerData
    .contains(PAYER_DATA.ARCHIVED_PAYER)
    .parents('tr')
    .within(() => {
      billingAdmin.archivePayerBtn.click();
    });
  cy.wait(500);
  cy.get('.swal2-confirm').click({ force: true });
  cy.wait(500);
  billingAdmin.payerArchived.eq(3).should('have.text', PAYER_DATA.ARCHIVED_TEXT);
};

const checkPayerDropdownPlan = () => {
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();
  cy.wait(500);
  billing.billingTabParents.click();
  billing.btnAddPlan.click();
  billing.selectPlanName.select(PAYER_DATA.PLAN_NAME, { force: true });
  billing.addAllocationBtn.click();
  billing.allocationType.select(PAYER_DATA.ALLOCATION_TYPE);
  billing.allocationReimbursementType.find('option').each(($option, index) => {
    cy.wrap($option)
      .invoke('text')
      .then((actualText) => {
        expect(actualText.trim()).to.equal(PAYERS_LIST[index]);
      });
  });
};

//dates for period config
const today = getFormattedDateIso();
const month = getFormattedDateIso(getFutureDate(30));
const nextMonth = getFormattedDateIso(getFutureDate(31));
const futureDateIso = getFormattedDateIso(getFutureDate(100));

const checkTimePeriod = () => {
  navigation.billingAdminConfigurationButton.click();
  navigation.loadingSpinner.should('not.exist');
  billingAdmin.settingsTab.click();
  billingAdmin.configureTimePeriod.click();
  billingAdmin.timePeriodItem.should('have.length', 8);
  billingAdmin.timePeriodItem
    .contains("[data-cy='time-period-name']", TIME_PERIOD_DATA.TIME_PERIOD)
    .parents('tr')
    .within(() => {
      billingAdmin.editTimePeriod.click();
    });

  billingAdmin
    .editPeriodItem(TIME_PERIOD_DATA.PERIOD_ID)
    .should('be.visible')
    .within(() => {
      billingAdmin.periodNameEdit.clear().type(TIME_PERIOD_DATA.TIME_PERIOD_UPDATED);
      billingAdmin.periodStarEdit.clear().type(today);
      billingAdmin.periodEndEdit.clear().type(month);
      billingAdmin.saveEdit.click();
    });

  cy.wait(500);

  billingAdmin.addNewTimePeriod.click();
  billingAdmin.periodNameInput.scrollIntoView().should('be.visible');
  billingAdmin.periodNameInput.type(TIME_PERIOD_DATA.NEW_TIME_PERIOD);
  billingAdmin.periodStartInput.type(nextMonth);
  billingAdmin.periodEndInput.type(futureDateIso);
  billingAdmin.saveTimePeriod.click();
  cy.wait(500);
  billingAdmin.timePeriodItem.should('have.length', 9);
  billingAdmin.timePeriodItem.each(($row, index) => {
    cy.wrap($row).within(() => {
      billingAdmin.timePeriodName.should('have.text', TIME_PERIOD[index]);
    });
  });
};

const checkDropDownTimePeriod = () => {
  billingAdmin.plansItemsTab.click();
  billing.addPlanBtn.click();
  billing.addPlanDateType.select(PLAN_DATA.PLAN_DATE_TYPE);

  //exclude first item because it is empty
  billing.addPlanServiceDates
    .find('option')
    .not(':first')
    .each(($option, index) => {
      const text = $option.text().split('(')[0].trim();
      expect(text).to.equal(PLAN_SERVICES_DATES[index]);
    });
};

const checkManagePrograms = () => {
  navigation.billingAdminConfigurationButton.click();
  navigation.loadingSpinner.should('not.exist');
  billingAdmin.settingsTab.click();
  billingAdmin.managePrograms.click();
  billingAdmin.programData.should('have.length', 4);
  billingAdmin.addNewProgram.click({ force: true });

  billingAdmin.addProgramName.scrollIntoView().should('be.visible');
  billingAdmin.addProgramName.type(MANAGE_PROGRAMS.TEST_PROGRAM);
  billingAdmin.addProgramStatus.select('Active');
  billingAdmin.checkRequiresAdvanceNotice.check({ force: true });
  billingAdmin.saveNewProgram.click({ force: true });
  cy.wait(500);
  billingAdmin.programName.should('contain', MANAGE_PROGRAMS.TEST_PROGRAM);

  billingAdmin.addNewProgram.click({ force: true });

  billingAdmin.addProgramName.scrollIntoView().should('be.visible');
  billingAdmin.addProgramName.type(MANAGE_PROGRAMS.AUTUMN_PROGRAM);
  billingAdmin.addProgramStatus.select('Active');
  billingAdmin.saveNewProgram.click();
  billingAdmin.programName.should('contain', MANAGE_PROGRAMS.AUTUMN_PROGRAM);
  billingAdmin.programData.should('have.length', 6);

  billingAdmin.programData
    .contains("[data-cy='program-name']", MANAGE_PROGRAMS.WINTER_PROGRAM)
    .parents('tr')
    .within(() => {
      billingAdmin.editProgram.click();
    });

  billingAdmin
    .editProgramData(MANAGE_PROGRAMS.PROGRAM_ID)
    .should('be.visible')
    .within(() => {
      billingAdmin.programNameEdit.clear().type(MANAGE_PROGRAMS.PROGRAM_UPDATED);
      billingAdmin.programStatusEdit.select('Inactive');
      billingAdmin.saveEditProgram.click();
    });
  cy.wait(500);
  cy.get('.swal2-confirm').click({ force: true });
  cy.wait(500);
  billingAdmin.programName.contains(MANAGE_PROGRAMS.PROGRAM_UPDATED).should('exist');
};

const checkDropDownProgram = () => {
  billingAdmin.plansItemsTab.click();
  billing.addPlanBtn.click();
  billing.addPlanProgram
    .find('option')
    .not(':first')
    .each((option, index) => {
      cy.wrap(option).should('have.text', PROGRAMS[index]);
    });
};
