import billingAdmin from 'cypress/pages/billing-admin';
import {
  AGING_WIDGET_DATA,
  AMOUNT_INVOICED_WIDGET_DATA,
  BASE_URLS,
  PAST_DUE_ACCOUNTS_WIDGET_DATA,
  PAYER_STATUS_WIDGET_DATA,
  REVENUE_WIDGET_DATA
} from 'cypress/support/constants';

context('Billing overview widgets', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.BILLING_ADMIN);
  });

  it('Checks accuracy of widgets in billing overview', () => {
    revenueWidget();
    amountInvoicedWidget();
    agingWidget();
    payerStatusWidget();
    pastDueAccountsWidget();
  });
});

const revenueWidget = () => {
  billingAdmin.overviewValues.eq(0).should('be.visible');
  billingAdmin.overviewValues.eq(0).contains(REVENUE_WIDGET_DATA.CHART_NAME);
  billingAdmin.overviewValues.eq(0).contains(REVENUE_WIDGET_DATA.REVENUE_VALUE);
  cy.get('#apexchartsRevenue-Performance').eq(0).should('exist');
  billingAdmin.footerBtn.eq(0).should('include.text', REVENUE_WIDGET_DATA.FOOTER);
};

const amountInvoicedWidget = () => {
  billingAdmin.overviewValues.eq(1).should('be.visible');
  billingAdmin.overviewValues.eq(1).contains(AMOUNT_INVOICED_WIDGET_DATA.CHART_NAME);
  billingAdmin.overviewValues.eq(1).contains(AMOUNT_INVOICED_WIDGET_DATA.AMOUNT_INVOICED_VALUE);
  billingAdmin.overviewValues.eq(1).contains(AMOUNT_INVOICED_WIDGET_DATA.DESCRIPTION);
  billingAdmin.footerBtn.eq(1).should('include.text', AMOUNT_INVOICED_WIDGET_DATA.FOOTER);
};

const agingWidget = () => {
  billingAdmin.overviewValues.eq(2).should('be.visible');
  billingAdmin.overviewValues.eq(2).contains(AGING_WIDGET_DATA.PAST_DUE_AMOUNT);
  billingAdmin.overviewValues.eq(2).within(() => {
    billingAdmin.chartDetails.should('be.visible');
    billingAdmin.chartDetails.contains(AGING_WIDGET_DATA.CHART_NAME);
  });
  cy.get('#apexchartsAging').should('exist');
  billingAdmin.footerBtn.eq(2).should('include.text', AGING_WIDGET_DATA.FOOTER);
};

const payerStatusWidget = () => {
  billingAdmin.overviewValues.eq(3).should('be.visible');
  billingAdmin.overviewValues.eq(3).contains(PAYER_STATUS_WIDGET_DATA.CHART_NAME);
  billingAdmin.overviewValues.eq(3).contains(PAYER_STATUS_WIDGET_DATA.DESCRIPTION);
  billingAdmin.payerStatusLabel.should('include.text', PAYER_STATUS_WIDGET_DATA.LABEL);
  billingAdmin.payerStatusOver30Days.should('include.text', PAYER_STATUS_WIDGET_DATA.OVER_30_DAYS);
  billingAdmin.payerStatusUnder30Days.should('include.text', PAYER_STATUS_WIDGET_DATA.UNDER_30_DAYS);
  billingAdmin.footerBtn.eq(3).should('include.text', PAYER_STATUS_WIDGET_DATA.FOOTER);
};

const pastDueAccountsWidget = () => {
  billingAdmin.overviewValues.eq(4).should('be.visible');
  billingAdmin.overviewValues.eq(4).contains(PAST_DUE_ACCOUNTS_WIDGET_DATA.CHART_NAME);
  billingAdmin.overviewValues.eq(4).contains(PAST_DUE_ACCOUNTS_WIDGET_DATA.DESCRIPTION);
  cy.get('#Past-Due-Accounts')
    .find('tbody tr')
    .should('have.length', PAST_DUE_ACCOUNTS_WIDGET_DATA.ROWS.length)
    .each((row, index) => {
      const expected = PAST_DUE_ACCOUNTS_WIDGET_DATA.ROWS[index];
      cy.wrap(row).within(() => {
        cy.get('a').should('have.text', expected.name);
        cy.get('span').should('have.text', expected.value);
      });
    });
};
