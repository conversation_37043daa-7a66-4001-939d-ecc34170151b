import activities from 'cypress/pages/activities';
import { BASE_URLS, CURRICULUM_TAGS, AVAILABLE_STANDARDS } from 'cypress/support/constants';

context('Configuration Activities Page Tests', () => {
  it('Check functionalities in content: configuration', () => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.ACTIVITIES_CONFIG);
    checkDisplayInfo();
    checkAddRoot();
    checkRenameTag();
    checkDeleteTag();
    checkAddChild();
    checkAvailableStandards();
  });
});

const checkDisplayInfo = () => {
  CURRICULUM_TAGS.forEach((tag) => {
    cy.contains(tag).should('be.visible');
  });
};

const checkAddRoot = () => {
  activities.curriculumTags.find('.jstree-node').each(($node) => {
    cy.wrap($node)
      .find('> .jstree-icon.jstree-ocl')
      .then(($icon) => {
        if ($icon.is(':visible')) {
          cy.wrap($icon).click({ force: true });
        }
      });
  });

  activities.curriculumTags.find('li').then(($nodes) => {
    const numberOfNodesBefore = $nodes.length;

    activities.addRootBtn.click();

    cy.get('.types-tree')
      .find('li')
      .should('have.length', numberOfNodesBefore + 1)
      .then(($newNodes) => {
        const numberOfNodesAfter = $newNodes.length;
        expect(numberOfNodesAfter).to.be.greaterThan(numberOfNodesBefore);
        activities.curriculumTags
          .find('input.jstree-rename-input')
          .should('exist')
          .then(($input) => {
            const newRootTag = 'New Root Tag';
            cy.wrap($input).type(`${newRootTag}{enter}`);
            cy.get('.types-tree')
              .find('li')
              .then(($updatedNodes) => {
                const newNodeId = Array.from($updatedNodes).reduce((maxId, node) => {
                  const nodeId = parseInt(node.id.split('_')[1]);
                  return Math.max(maxId, nodeId);
                }, 0);

                const newNodeSelector = `#j1_${newNodeId}_anchor`;
                cy.get(newNodeSelector).should('contain', newRootTag);
              });
          });
      });
  });
};

const checkRenameTag = () => {
  activities.curriculumTags
    .find('li')
    .first()
    .then(($node) => {
      const nodeId = $node.attr('id');
      const newName = 'Renamed Node';
      cy.get(`#${nodeId}_anchor`).click();
      activities.renameTagBtn.click();

      activities.curriculumTags.find('input.jstree-rename-input').type(`${newName}{enter}`);
      cy.get(`#${nodeId}_anchor`).should('contain', newName);
    });
};

const checkDeleteTag = () => {
  activities.curriculumTags.find('li').then(($nodes) => {
    const numberOfNodesBefore = $nodes.length;
    activities.curriculumTags
      .find('li')
      .last()
      .then(($node) => {
        const nodeId = $node.attr('id');
        cy.get(`#${nodeId}_anchor`).click();
        activities.deleteTagBtn.click();

        activities.curriculumTags.find('li').should('have.length', numberOfNodesBefore - 1);
      });
  });
};

const checkAddChild = () => {
  activities.curriculumTags
    .find('li')
    .first()
    .then(($node) => {
      const nodeId = $node.attr('id');
      const newChildName = 'New Child Node';
      cy.get(`#${nodeId}_anchor`).click();
      activities.addChildBtn.click();
      activities.curriculumTags.find('input.jstree-rename-input').type(`${newChildName}{enter}`);
      cy.get(`#${nodeId}`).find('ul.jstree-children li').last().should('contain', newChildName);
    });
};

const checkAvailableStandards = () => {
  activities.availableStandardsTab.click();
  activities.standardsTable.should('have.length.at.least', 3);
  activities.standardBenchmark.eq(0).should('include.text', AVAILABLE_STANDARDS[0].benchmark);
  for (let n = 0; n < 3; n++) {
    activities.standardBenchmark.eq(n).should('include.text', AVAILABLE_STANDARDS[n].benchmark);
  }
};
