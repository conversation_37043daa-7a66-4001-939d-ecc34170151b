import { getFormattedDatesForActivities } from 'cypress/e2e/utils';
import activities from 'cypress/pages/activities';
import navigation from 'cypress/pages/navigation';
import { ACTIVITIES_EDITED, BASE_URLS, GROUPS, NEW_SCHEDULED_ACTIVITIES } from 'cypress/support/constants';

context('Scheduled Activities Page Tests', () => {
  beforeEach(() => {
    cy.clearAndSeedCustomDatabase();
    cy.login(BASE_URLS.SCHEDULED_ACTIVITIES_URL);
  });

  it('Check functionalities in content: scheduled activities', () => {
    checkLayoutFunctionalitiesScheduledActivities();
  });
});

const checkLayoutFunctionalitiesScheduledActivities = () => {
  const dates = getFormattedDatesForActivities();
  //Adding new theme
  activities.scheduledActivitiesBtn.click();
  activities.themeName.type(NEW_SCHEDULED_ACTIVITIES[1].name);
  activities.descriptionTheme.type(NEW_SCHEDULED_ACTIVITIES[1].description);
  activities.selectGroups.select(NEW_SCHEDULED_ACTIVITIES[1].recipients, { force: true });
  activities.btnCreateTheme.click();
  activities.addDay.click();
  cy.get('td.today.day').click();
  activities.addSelectedDays.click();
  activities.addActivityNew.click();
  cy.wait(1000);
  cy.get('.select2-selection', { timeout: 1000 }).click();
  cy.contains('.select2-results__option', NEW_SCHEDULED_ACTIVITIES[1].theme).click();
  activities.copyExistingActivityBtn.click();
  cy.wait(500);
  activities.curriculumBankActivityFormSave.click();
  cy.wait(1000);
  activities.scheduledActivitiesHeader.click();
  cy.wait(1000);

  //Adding new theme
  activities.scheduledActivitiesBtn.click();
  activities.themeName.type(NEW_SCHEDULED_ACTIVITIES[2].name);
  activities.descriptionTheme.type(NEW_SCHEDULED_ACTIVITIES[2].description);
  activities.btnCreateTheme.click();
  activities.addDay.click();
  activities.addNextWeekday.click();
  activities.addActivityNew.click();
  cy.wait(1000);
  cy.get('.select2-selection', { timeout: 1000 }).click();
  cy.contains('.select2-results__option', NEW_SCHEDULED_ACTIVITIES[2].theme).click();
  activities.copyExistingActivityBtn.click();
  cy.wait(500);
  activities.curriculumBankActivityFormSave.click();
  cy.wait(1000);
  activities.scheduledActivitiesHeader.click();
  cy.wait(1000);

  //Adding theme without scheduled activities
  activities.scheduledActivitiesBtn.click();
  activities.themeName.type(NEW_SCHEDULED_ACTIVITIES[0].name);
  activities.descriptionTheme.type(NEW_SCHEDULED_ACTIVITIES[0].description);
  activities.btnCreateTheme.click();
  activities.addDay.click();
  cy.get('td.today.day').click();
  activities.addSelectedDays.click();
  activities.scheduledActivitiesHeader.click();
  activities.startDateActivities.clear();
  activities.startDateActivities.type(dates.today);
  cy.get('td.active.day').click();

  //Check table content
  for (let n = 0; n < 3; n++) {
    activities.scheduledThemeName.eq(n).should('include.text', NEW_SCHEDULED_ACTIVITIES[n].themeName);
    activities.scheduledNameOfTheme.eq(n).should('include.text', NEW_SCHEDULED_ACTIVITIES[n].name);
    activities.recipients.eq(n).should('include.text', NEW_SCHEDULED_ACTIVITIES[n].recipients);
  }

  activities.scheduledDate.eq(0).should('include.text', dates.today);
  activities.scheduledDate.eq(1).should('include.text', dates.today);
  activities.scheduledDate.eq(2).should('include.text', dates.nextWeekday);

  /*//Check filtering by Date
    activities.startDateActivities.clear();
    activities.startDateActivities.type(formattedDateTomorrow);
    cy.get('td.active.day').click();
    activities.scheduledThemeName.should("have.length", 1);
    activities.scheduledActivitiesHeader.click();*/

  //Check filter by group
  activities.scheduledThemeName.should('have.length', 3);
  activities.scheduledActivitiesGroupFilter.select(GROUPS.KINDERGARDEN.NAME);
  activities.scheduledThemeName.should('have.length', 1);
  activities.scheduledActivitiesGroupFilter.select('All');

  //Check functionalities
  activities.scheduledActivitiesDropdown.eq(0).click();
  activities.manageTheme.eq(0).click();
  cy.wait(1000);
  navigation.loadingSpinner.should('not.exist');
  cy.wait(500);
  activities.btnUpdateTheme.click({ force: true });
  cy.contains('OK', { timeout: 5000 }).click();
  activities.addActivityNew.click();
  cy.wait(1000);
  cy.get('.select2-selection', { timeout: 1000 }).click();
  cy.contains('.select2-results__option', ACTIVITIES_EDITED.THEME).click();
  activities.copyExistingActivityBtn.click();
  cy.wait(500);
  activities.curriculumBankActivityFormSave.click();
  cy.wait(1000);
  activities.scheduledActivitiesHeader.click();
  cy.wait(1000);
  navigation.loadingSpinner.should('not.exist');
  cy.wait(500);
  activities.startDateActivities.clear();
  activities.startDateActivities.type(dates.today);
  cy.get('td.active.day').click();
  activities.scheduledThemeName.eq(1).should('include.text', ACTIVITIES_EDITED.THEME_NAME);

  activities.scheduledActivitiesHeader.click();
  cy.wait(1000);
  cy.get('div.btn.btn-icon.btn-clean[data-toggle="dropdown"]').eq(2).click();
  activities.manageTheme.eq(2).click({ force: true });
  cy.wait(5000);
  navigation.loadingSpinner.should('not.exist');
  cy.wait(500);
  activities.btnUpdateTheme.click({ force: true });
  cy.contains('OK', { timeout: 5000 }).click();
  activities.dropdownScheduledDay.click();
  activities.optionDelete.click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  activities.btnDeleteTheme.click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  activities.scheduledActivitiesHeader.click();
  activities.startDateActivities.clear();
  activities.startDateActivities.type(dates.today);
  cy.get('td.active.day').click();
  activities.scheduledThemeName.should('have.length', 2);

  /*//Check filter by search text theme
    activities.scheduledActivitiesHeader.click();
    activities.groupByThemes.check({ force: true });
    activities.searchTextActivities.type("TestActivity{enter}");
    cy.wait(500);
    activities.scheduledThemeName.should("have.length", 2);*/
};
