import activities from 'cypress/pages/activities';
import { ACTIVITIES_GLOBAL, BASE_URLS, NEW_ACTIVITIES } from 'cypress/support/constants';
import admin from 'cypress/pages/admin';
import { paginationFlow, typeDraftTheme } from './utils';
import navigation from 'cypress/pages/navigation';
import { getFormattedDatesForActivities } from 'cypress/e2e/utils';

context('All Activities Page Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('should verify pagination controls all activities page', () => {
    cy.login(BASE_URLS.ACTIVITIES_URL);
    paginationFlow();
  });

  it('Check functionalities in content: all activities', () => {
    cy.login(BASE_URLS.ACTIVITIES_URL);
    checkLayoutFunctionalitiesAllActivities();
    checkLocalandGlobalActivititesAvailable();
  });
});

const checkLayoutFunctionalitiesAllActivities = () => {
  //Adds published activity
  activities.createNewActivityBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  cy.get('input[name="headline"]', { timeout: 3000 }).type(NEW_ACTIVITIES[2].name);
  cy.get('#inputAge').select(NEW_ACTIVITIES[2].ageGroups);
  admin.saveEdits.click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);

  //Adds draft activity
  activities.createNewActivityBtn.should('be.visible').click({ force: true });
  cy.wait(3000);
  cy.get('input[name="headline"]', { timeout: 3000 }).type(NEW_ACTIVITIES[3].name);
  cy.get('#inputAge').select(NEW_ACTIVITIES[3].ageGroups);
  admin.saveEdits.click();
  typeDraftTheme();

  //Checks sorting and filtering using search and Age Groups
  activities.searchActivityBtn.click();
  cy.wait(500);
  activities.activityTitle.should('have.length', 4);
  activities.inputActivitySearch.type('Test');
  activities.searchActivityBtn.click();
  cy.wait(500);
  activities.activityTitle.should('have.length', 2);
  activities.ageActivitySelector.select(NEW_ACTIVITIES[2].ageGroups);
  cy.wait(2000);
  activities.activityTitle.should('have.length', 1);

  //Checks dropdown functionalities and table content
  activities.dropdownActivityOptions.click();
  activities.editActivity.click();
  cy.get('textarea#inputMessage').type('Message Test');
  admin.saveEdits.click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(500);

  activities.ageActivitySelector.select('All');
  activities.inputActivitySearch.clear();
  activities.searchActivityBtn.click();
  cy.wait(500);

  for (let n = 0; n < 4; n++) {
    activities.activityTitle.eq(n).should('include.text', NEW_ACTIVITIES[n].name);
    activities.activityAgeGroup.eq(n).should('include.text', NEW_ACTIVITIES[n].ageGroups);
    activities.activitySource.eq(n).should('include.text', NEW_ACTIVITIES[n].source);
    activities.createdByName.eq(n).should('include.text', NEW_ACTIVITIES[n].createdBy);
    activities.createdByOrg.eq(n).should('include.text', NEW_ACTIVITIES[n].createdOrg);
    activities.dropdownActivityOptions.eq(n).should('include.text', NEW_ACTIVITIES[n].type);
  }
  activities.modifiedByName.eq(0).should('include.text', NEW_ACTIVITIES[2].createdBy);
  activities.modifiedByOrg.eq(0).should('include.text', NEW_ACTIVITIES[2].createdOrg);
  activities.approvedByName.eq(0).should('include.text', NEW_ACTIVITIES[2].createdBy);
  activities.approvedByOrg.eq(0).should('include.text', NEW_ACTIVITIES[2].createdOrg);

  activities.ageActivitySelector.select(NEW_ACTIVITIES[3].ageGroups);
  cy.wait(1000);
  activities.dropdownActivityOptions.click();
  activities.publishActivity.click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  activities.allActivitiesHeader.click();
  cy.wait(1000);
  activities.ageActivitySelector.select(NEW_ACTIVITIES[3].ageGroups);
  cy.wait(1000);
  activities.dropdownActivityOptions.should('include.text', NEW_ACTIVITIES[2].type);

  activities.allActivitiesHeader.click();
  cy.wait(1000);
  activities.ageActivitySelector.select(NEW_ACTIVITIES[2].ageGroups);
  cy.wait(1000);
  activities.dropdownActivityOptions.click();
  activities.unpublishActivity.click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  activities.allActivitiesHeader.click();
  cy.wait(1000);
  activities.ageActivitySelector.select(NEW_ACTIVITIES[2].ageGroups);
  cy.wait(1000);
  activities.dropdownActivityOptions.should('include.text', NEW_ACTIVITIES[3].type);

  activities.dropdownActivityOptions.click();
  activities.deleteActivity.click();
  cy.contains('Yes, delete it').click({ force: true });
  cy.wait(1000);
  cy.contains('OK', { timeout: 5000 }).click();
  cy.wait(1000);
  activities.allActivitiesHeader.click();
  cy.wait(1000);
  activities.activityTitle.should('have.length', 3);

  activities.dropdownActivityOptions.eq(0).click();
  activities.reviewActivity.click();
  activities.approveReviewBtn.click();
  cy.contains('OK', { timeout: 5000 }).click();
  activities.approvedByName.eq(0).should('include.text', NEW_ACTIVITIES[2].createdBy);
  activities.approvedByOrg.eq(0).should('include.text', NEW_ACTIVITIES[2].createdOrg);
};

const checkLocalandGlobalActivititesAvailable = () => {
  const dates = getFormattedDatesForActivities();
  cy.visit(BASE_URLS.SCHEDULED_ACTIVITIES_URL);
  cy.wait(1000);
  navigation.loadingSpinner.should('not.exist');
  
  activities.startDateActivities.clear();
  activities.startDateActivities.type(dates.today);
  cy.get('td.active.day').click();
  
  activities.scheduledActivitiesBtn.click();
  navigation.loadingSpinner.should('not.exist');
  cy.wait(2000);
  activities.themeName.type(ACTIVITIES_GLOBAL.THEME);
  activities.descriptionTheme.type(ACTIVITIES_GLOBAL.DESCRIPTION);
  activities.btnCreateTheme.click();
  activities.addDay.click();
  cy.get('td.today.day').click();
  activities.addSelectedDays.click();
  activities.addActivityNew.click();
  cy.wait(2000);
  cy.get('.select2-selection', { timeout: 2000 }).click();
  cy.contains('.select2-results__option', ACTIVITIES_GLOBAL.LOCAL_ACTIVITY);
  cy.contains('.select2-results__option', ACTIVITIES_GLOBAL.GLOBAL_ACTIVITY).click();
  activities.copyExistingActivityBtn.click();
  cy.wait(500);
  activities.curriculumBankActivityFormSave.click();
  cy.wait(1000);
  
  activities.scheduledActivitiesHeader.click();
  cy.wait(1000);
  
  activities.startDateActivities.clear();
  activities.startDateActivities.type(dates.today);
  cy.get('td.active.day').click();

  activities.scheduledThemeName.should('include.text', ACTIVITIES_GLOBAL.GLOBAL_ACTIVITY_NAME);
};
