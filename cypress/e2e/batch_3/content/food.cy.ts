import food from 'cypress/pages/food';
import { BASE_URLS, FOOD_DATA, FOOD_EDIT, GROUPS } from 'cypress/support/constants';
import { getFormattedDateIso, getFormattedFutureDate, getFutureDate } from '../../utils';

context('Food page Tests', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
  });

  it('should verify functionalities in food page', () => {
    cy.login(BASE_URLS.FOOD);
    addingNewFood();
    checkContent();
    checkfiltering();
    checkFunctionalities();
    checkFoodEntry();
  });
});

const formattedTomorrow: string = getFormattedFutureDate(1);
const formattedDayAfterTomorrow: string = getFormattedFutureDate(2);
const formattedDateIso = getFormattedDateIso();
const formattedTomorrowIso = getFormattedDateIso(getFutureDate(1));

const addingNewFood = () => {
  //Checking no message food
  food.noFoodMessage.should('be.visible');
  food.visitCalendarBtn.click();
  cy.url().should('include', BASE_URLS.CALENDAR);
  cy.visit(BASE_URLS.FOOD);

  //Adding Breakfast
  food.addFoodBtnMsg.click();
  food.breakfastBtn.click();
  food.foodItemDescription.type(FOOD_DATA[0].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[0].description);
  food.scheduledFoodToday.click({ force: true });
  food.saveFoodBtn.click();

  //Adding AM Snack
  food.addNewFood.click();
  food.amSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[1].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[1].description);
  food.scheduledFoodToday.click({ force: true });
  food.saveFoodBtn.click();

  //Adding Lunch
  food.addNewFood.click();
  food.lunchBtn.click();
  food.foodItemDescription.type(FOOD_DATA[2].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[2].description);
  food.scheduledFoodToday.click({ force: true });
  food.selectedGroupsChk.click({ force: true });
  food.selectGroupsInput.select(FOOD_DATA[2].recipients, { force: true });
  food.saveFoodBtn.click();

  //Add PM Snack
  food.addNewFood.click();
  food.pmSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[3].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[3].description);
  food.scheduledFoodToday.click({ force: true });
  food.selectedGroupsChk.click({ force: true });
  food.selectGroupsInput.select(FOOD_DATA[3].recipients, { force: true });
  food.saveFoodBtn.click();

  //Add Late Snack
  food.addNewFood.click();
  food.lateSnackBtn.click();
  food.foodItemDescription.type(FOOD_DATA[4].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[4].description);
  food.scheduledFoodFuture.click({ force: true });
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.repeatThursday.check({ force: true });
  food.repeatFriday.check({ force: true });
  food.repeatSaturday.check({ force: true });

  food.setEndDate.click({ force: true });
  food.setEndDateInput.type(formattedTomorrow);
  food.saveFoodBtn.click();

  //Add Dinner
  food.addNewFood.click();
  food.dinnerBtn.click();
  food.foodItemDescription.type(FOOD_DATA[5].item);
  food.addFoodItemModal.click();
  food.descriptionFoodInput.type(FOOD_DATA[5].description);

  food.scheduledFoodFuture.click({ force: true });
  food.recurrenceOption.select('Repeats');
  food.repeatsEveryWeek.type('1');

  food.repeatSunday.check({ force: true });
  food.repeatMonday.check({ force: true });
  food.repeatTuesday.check({ force: true });
  food.repeatWednesday.check({ force: true });
  food.repeatThursday.check({ force: true });
  food.repeatFriday.check({ force: true });
  food.repeatSaturday.check({ force: true });

  food.selectedGroupsChk.click({ force: true });
  food.selectGroupsInput.select(FOOD_DATA[5].recipients, { force: true });
  food.saveFoodBtn.click();
};

const checkContent = () => {
  for (let n = 0; n < 6; n++) {
    food.foodTitle.eq(n).should('include.text', FOOD_DATA[n].type);
    food.foodDescription.eq(n).should('include.text', FOOD_DATA[n].item);
    food.foodDate.eq(n).should('include.text', formattedDateIso);
    food.foodRecipients.eq(n).should('include.text', FOOD_DATA[n].recipients);
  }

  for (let n = 6; n < 8; n++) {
    food.foodDate.eq(n).should('include.text', formattedTomorrowIso);
  }
};

const checkfiltering = () => {
  //Filtering by Group
  food.filterByGroup.select(GROUPS.TODDLER.NAME);
  food.foodTitle.should('have.length', 1);
  food.filterByGroup.select(GROUPS.KINDERGARDEN.NAME);
  food.foodTitle.should('have.length', 9);

  //Filtering by Date
  food.startDateFilter.clear();
  food.startDateFilter.type(formattedTomorrow);
  cy.get('td.active.day').click();
  food.endDateFilter.clear();
  food.endDateFilter.type(formattedDayAfterTomorrow);
  cy.get('td.active.day').click();
  food.foodTitle.should('have.length.at.least', 1);
  cy.reload();
};

const checkFunctionalities = () => {
  //Edit
  food.dropdownOptions.eq(0).click();
  food.editFood.eq(0).click();
  food.foodItemDescription.type(FOOD_EDIT.DESCRIPTION);
  food.addFoodItemModal.click();
  food.scheduledFoodToday.click({ force: true });
  food.saveFoodBtn.click();
  food.foodDescription.eq(0).should('include.text', FOOD_DATA[0].item).and('include.text', FOOD_EDIT.DESCRIPTION);

  //Delete
  food.dropdownOptions.eq(0).click();
  food.deleteFood.eq(0).click();
  cy.contains('Yes, delete it!').click();
  food.foodTitle.should('have.length', 13);
};

const checkFoodEntry = () => {
  food.foodTitle.last().click();
  food.mealTypeLabel.should('include.text', FOOD_DATA[5].type);
  food.foodEntryLabel.should('include.text', FOOD_DATA[5].item);
  food.scheduledDateLabel.should('include.text', FOOD_EDIT.SCHEDULED_LABEL);
  food.descriptionLabel.should('include.text', FOOD_DATA[5].description);
  food.recurringDescriptionLabel.should('include.text', FOOD_EDIT.RECURRING_DESCRIPTION_LABEL);
  food.selectedGroupNamesLabel.should('include.text', FOOD_DATA[5].recipients);

  food.editFoodEntryBtn.click();
  food.descriptionFoodInput.should('be.visible');
  food.closeFoodBtn.click();
  food.toggleDropdownEntry.click();
  food.deleteFoodEntryOpt.click();
  cy.contains('Yes, Delete Series').click();
  food.foodTitle.should('have.length', 5);
};
