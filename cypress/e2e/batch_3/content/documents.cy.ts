import documents from 'cypress/pages/documents';
import { BASE_URLS, DOCUMENTS_DATA, DOCUMENTS_EDITED, GROUPS } from 'cypress/support/constants';

context('Documents page Tests', () => {
  it('should verify functionalities in documents page', () => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.DOCUMENTS);
    addNewDocument();
    checkTableContent();
    checkFuncionalities();
  });
});

const addNewDocument = () => {
  //New Document
  documents.addDocumentBtn.click();
  documents.documentNameInput.type(DOCUMENTS_DATA[1].name);
  documents.templateOptionsInput.select(DOCUMENTS_EDITED.TEMPLATE_OPTION);
  documents.assignmentIndividualsOnly.check({ force: true });
  documents.groupingInput.type(DOCUMENTS_DATA[1].section);
  documents.saveDocumentBtn.click();

  //Archived Document
  documents.addDocumentBtn.click();
  documents.documentNameInput.type(DOCUMENTS_DATA[0].name);
  documents.templateOptionsInput.select(DOCUMENTS_DATA[0].templateOptions);
  documents.assignmentGroups.check({ force: true });
  documents.selectGroupsInput.select(GROUPS.TODDLER.NAME, { force: true });
  documents.groupingInput.type(DOCUMENTS_DATA[0].section);
  documents.saveDocumentBtn.click();
};

const checkTableContent = () => {
  //Archive Document
  documents.archiveDocument.eq(0).click();
  cy.contains('OK', { timeout: 5000 }).click();
  cy.contains('Document removed');
  cy.containsOkAndClick();
  documents.nameLabel.should('have.length', 4);

  documents.showArchivedDocuments.check({ force: true });
  documents.nameLabel.should('have.length', 5);
  documents.archivedLabel.eq(0).should('be.visible');

  for (let n = 0; n < 5; n++) {
    documents.sectionLabel.eq(n).should('include.text', DOCUMENTS_DATA[n].section);
    documents.nameLabel.eq(n).should('include.text', DOCUMENTS_DATA[n].name);
    documents.templateOptions.eq(n).should('include.text', DOCUMENTS_DATA[n].templateOptions);
    documents.groupsDescription.eq(n).should('include.text', DOCUMENTS_DATA[n].groups);
  }
};

const checkFuncionalities = () => {
  //Edit Document
  documents.editDocument.eq(0).click();
  documents.documentNameInput.clear();
  documents.documentNameInput.type(DOCUMENTS_EDITED.NAME);
  documents.groupingInput.clear();
  documents.groupingInput.type(DOCUMENTS_EDITED.SECTION);
  documents.saveDocumentBtn.click();

  //Check Edited Document
  documents.sectionLabel.eq(0).should('include.text', DOCUMENTS_EDITED.SECTION);
  documents.nameLabel.eq(0).should('include.text', DOCUMENTS_EDITED.NAME);

  //Check View button for document
  documents.viewFile.should('be.visible');
};
