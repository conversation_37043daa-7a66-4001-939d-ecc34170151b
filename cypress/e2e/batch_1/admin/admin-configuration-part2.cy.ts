import {
  BASE_URLS,
  CHILD_DATA,
  SCHEDULED_TYPES_EDITED,
  SCHEDULE_TYPES,
  SCHEDULE_TYPE_ADD,
  SCHEDULE_TYPE_EDIT,
  INMUNIZATIONS,
  CUSTOM_INMUNIZATION,
  HOLIDAYS,
  RETURN_ADDRESS,
  GROUPS,
  NEW_PLAN_CHILD
} from '../../../support/constants';
import Admin from 'cypress/pages/admin';
import header from 'cypress/pages/header';
import navigation from 'cypress/pages/navigation';
import search from 'cypress/pages/search';
import people from 'cypress/pages/people';
import admin from 'cypress/pages/admin';
import groups from 'cypress/pages/groups';
import messageCenter from 'cypress/pages/message-center';
import billing from '../../../pages/billing';

context('Admin Configuration Part 2', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.ADMIN);
  });

  it('Checks inmunizations', () => {
    checkStandardInmunizations();
    checkCustomInmunization();
  });

  it('Checks schedule types configuration', () => {
    checkScheduleTypeContent();
    editScheduleType();
    deleteScheduleType();
    addScheduleType();
    checkScheduleTypesChildProfile();
    suspendBillingPlans();
    checkNoBillingPlansAvailableErrorMessage();
  });

  it('Checks administration visibility conf, message center configuration and holiday configuration', () => {
    checkMessageCenterConfig();
    checkDisableStaffMessageCenter();
    checkConfigStaffMessaggeCenter();
    checkEnableStaffMessaggeCenter();
    checkReturnAddress();
    checkHolidaysConfiguration();
  });
});

const checkStandardInmunizations = () => {
  admin.configureStandardInmunization.click();
  admin.disableInmunizationReminder.check({ force: true });
  INMUNIZATIONS.forEach((immunization) => {
    admin.standardInmunization(immunization.type).within(() => {
      admin.inmunizationType.should('contain', immunization.type);
      admin.inmunizationDescription.should('contain', immunization.description);
    });
  });

  admin.inmunizationExempt(INMUNIZATIONS[3].type).check({ force: true });
  admin.inmunizationSave(INMUNIZATIONS[3].type).click();
  cy.containsOkAndClick();

  admin.inmunizationAnnual(INMUNIZATIONS[4].type).check({ force: true });
  admin.inmunizationSave(INMUNIZATIONS[4].type).click();
  cy.containsOkAndClick();

  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();
  people.profileHeaderNavigation.click();

  people.inmunizationsTable.should('exist').click();
  INMUNIZATIONS.forEach((inmunization) => {
    people.inmunizationType.contains(inmunization.type).should('exist');
  });
};

const checkCustomInmunization = () => {
  navigation.adminNav.click();
  admin.configureCustomInmunization.click();
  addInmunizationType(CUSTOM_INMUNIZATION, CUSTOM_INMUNIZATION);
  verifyInmunizationTypes(3);
  admin.checkboxAnnual.eq(1).check({ force: true });
  admin.saveUpdateInmunization.eq(1).click();
  cy.containsOkAndClick();
  admin.checkboxArchived.eq(2).check({ force: true });
  admin.saveUpdateInmunization.eq(2).click();
  cy.containsOkAndClick();

  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();
  people.profileHeaderNavigation.click();
  people.inmunizationType.contains(CUSTOM_INMUNIZATION[0]).should('exist');
  people.inmunizationType.contains(CUSTOM_INMUNIZATION[1]).should('exist');
  people.inmunizationType.contains(CUSTOM_INMUNIZATION[2]).should('not.exist');
};

function addInmunizationType(types: string[], descriptions: string[]) {
  types.forEach((type, index) => {
    admin.addInmunizationTypeBtn.click({ force: true });
    admin.addInmunizationType.type(type);
    admin.addInmunizationDescription.type(descriptions[index]);
    admin.saveInmunization.click();
    cy.wait(500);
  });
}

function verifyInmunizationTypes(expectedLength: number) {
  admin.inmunizationTypeItem.should('have.length', expectedLength);
}

const checkScheduleTypeContent = () => {
  Admin.configureScheduleTypes.scrollIntoView().click();
  for (let n = 0; n < 5; n++) {
    Admin.nameScheduleType.eq(n).should('include.text', SCHEDULE_TYPES[n].name);
    Admin.startTime.eq(n).should('include.text', SCHEDULE_TYPES[n].startTime);
    Admin.endTime.eq(n).should('include.text', SCHEDULE_TYPES[n].endTime);
    Admin.fteCount.eq(n).should('include.text', SCHEDULE_TYPES[n].fte);
    Admin.hiddenInForecastOpt.eq(n).should('include.text', SCHEDULE_TYPES[n].hiddenInForecast);
  }
};

const editScheduleType = () => {
  Admin.editScheduleTypeBtn.first().click();
  Admin.inputScheduleTypeName.clear();
  Admin.inputScheduleTypeName.type(SCHEDULE_TYPE_EDIT.NAME);
  Admin.fteCountInput.clear();
  Admin.fteCountInput.type(SCHEDULE_TYPE_EDIT.FTE);
  Admin.saveAddScheduleType.click();
  cy.wait(500);
  cy.get('.swal2-confirm').click({ force: true });
  cy.containsOkAndClick();
  cy.reload();
  Admin.nameScheduleType.should('have.length', 5);
  Admin.nameScheduleType.first().should('include.text', SCHEDULE_TYPE_EDIT.NAME);
  Admin.fteCount.first().should('include.text', SCHEDULE_TYPE_EDIT.FTE);
};

const deleteScheduleType = () => {
  Admin.deleteScheduleTypeBtn.last().click({ force: true });
  cy.get('.swal2-confirm').click({ force: true });
  cy.wait(500);
  cy.containsOkAndClick();
  Admin.nameScheduleType.should('have.length', 4);
};

const addScheduleType = () => {
  Admin.configureScheduleTypes.scrollIntoView().click();
  Admin.addScheduleTypeBtn.scrollIntoView().click();
  cy.wait(1000);

  Admin.inputScheduleTypeName.should('be.visible');
  Admin.inputScheduleTypeName.type(SCHEDULE_TYPE_ADD.NAME);
  Admin.hideFromForecastingCheck.check({ force: true });
  Admin.fteCountInput.clear();
  Admin.fteCountInput.type(SCHEDULE_TYPE_ADD.FTE);
  Admin.saveAddScheduleType.click();
  cy.wait(500);
  cy.get('.swal2-confirm').click({ force: true });
  cy.containsOkAndClick();
  Admin.nameScheduleType.should('have.length', 5);
  Admin.nameScheduleType.last().should('include.text', SCHEDULE_TYPE_ADD.NAME);
  Admin.fteCount.last().should('include.text', SCHEDULE_TYPE_ADD.FTE);
  Admin.hiddenInForecastOpt.last().should('include.text', SCHEDULE_TYPE_ADD.HIDDEN_FORECAST);
};

const checkScheduleTypesChildProfile = () => {
  search.searchPeopleIcon.click();
  search.searchPeopleInp.clear().type(CHILD_DATA.FIRST_NAME);
  search.searchManageButton.first().click();

  people.schedulingTab.click();
  people.newScheduleItem.click();
  cy.get('[data-cy="target-group"]').select(GROUPS.TODDLER.NAME);
  people.scheduleDate.clear();
  people.scheduleDate.type('01/11/2024', { force: true });

  SCHEDULED_TYPES_EDITED.forEach((option) => {
    people.scheduleType.find(`option:contains('${option}')`).should('exist');
  });

  people.scheduleType.select(SCHEDULED_TYPES_EDITED[1], { force: true });
  billing.selectPlanName.select(NEW_PLAN_CHILD.PLAN_NAME, { force: true });
  people.saveScheduleEntry.click();
  cy.containsOkAndClick();
};

const suspendBillingPlans = () => {
  navigation.billingNav.click();
  navigation.billingAdminConfigurationButton.click();
  cy.get('#dropdown-pDXgnrGpWy3emx5Ja').click();
  cy.get('[data-cy="archive-button-pDXgnrGpWy3emx5Ja"]').click();
  cy.get('.swal2-confirm').click();
  cy.get('.swal2-confirm').click();

  cy.get('#dropdown-Zkcq536vvL4pJEKYG').click();
  cy.get('[data-cy="archive-button-Zkcq536vvL4pJEKYG"]').click();
  cy.get('.swal2-confirm').click();
  cy.get('.swal2-confirm').click();
};

const checkNoBillingPlansAvailableErrorMessage = () => {
  navigation.manageNav.click();
  navigation.groupsNav.click();
  groups.groupRosterBtn.eq(0).click();
  cy.wait(500);
  people.personName.eq(0).click();
  cy.wait(500);
  people.schedulingTab.click();
  people.newScheduleItem.click();
  people.saveScheduleEntry.click();
  cy.get('#swal2-html-container', { timeout: 2000 }).contains(
    'No billing plans available - Update your configuration in Groups > Billing Plans'
  );
  cy.get('.swal2-confirm').click();
  people.scheduleType.select('Test');
  people.saveScheduleEntry.click();
  cy.get('#swal2-html-container', { timeout: 2000 }).should(
    'not.contain',
    'No billing plans available - Update your configuration in Groups > Billing Plans'
  );
};

const checkMessageCenterConfig = () => {
  //Check disable administrative visibility
  admin.configureMessageCenter.click();
  admin.disableAdministrativeVisibility.click();
  confirm();
  header.messageCenterBtn.click({ force: true });
  messageCenter.adminViewSection.should('not.exist');

  //Check enable administrative visibility
  navigation.adminNav.click();
  admin.configureMessageCenter.click();
  admin.enableAdministrativeVisibility.click();
  confirm();
  header.messageCenterBtn.click();
  messageCenter.adminViewSection.should('exist');
  messageCenter.adminViewSection.click();

  //Disable staff message center
  navigation.adminNav.click();
  admin.configureMessageCenter.click();
  admin.disableStaffMessageCenter.click();
  confirm();
  cy.performLogout();
};

const checkDisableStaffMessageCenter = () => {
  cy.login(BASE_URLS.DEFAULT, 'Staff');
  header.messageCenterBtn.should('not.exist');
  cy.performLogout();
};

const checkConfigStaffMessaggeCenter = () => {
  cy.login(BASE_URLS.ADMIN);
  admin.configureMessageCenter.click();
  admin.enableStaffMessageCenter.click();
  confirm();
  cy.performLogout();
};

const checkEnableStaffMessaggeCenter = () => {
  cy.login(BASE_URLS.DEFAULT, 'Staff');
  header.messageCenterBtn.should('exist');
  header.messageCenterBtn.click();
  cy.performLogout();
};

const checkReturnAddress = () => {
  cy.login(BASE_URLS.ADMIN);
  navigation.adminNav.click();
  admin.configureReplyToAddress.click();
  cy.get('.swal2-popup').should('be.visible');
  cy.get('.swal2-title').should('have.text', RETURN_ADDRESS.TITLE);
  cy.get('.swal2-input').type(RETURN_ADDRESS.EMAIL);
  cy.get('.swal2-confirm').click({ force: true });
};

const checkHolidaysConfiguration = () => {
  admin.configureHolidays.click();
  cy.wait(1000); // Wait for panel to open

  // Test individual date holidays
  cy.get('#holidayDateType').should('have.value', 'individual');
  addIndividualHolidays();

  cy.get('#btnAddHoliday').should('be.visible').click({ force: true });
  cy.wait(500);

  cy.get('#holidayDateType').should('be.visible').select('range', { force: true });
  cy.wait(500);
  admin.holidayName.should('be.visible').type('Christmas Break');
  cy.get('#holidayStartDate').should('be.visible').type('12/24/2030', { force: true });
  cy.get('td.active.day').click();
  cy.get('#holidayEndDate').should('be.visible').type('12/26/2030', { force: true });
  cy.get('td.active.day').click();
  admin.saveAddHoliday.click();

  // Handle the confirmation modal
  cy.get('#confirmHolidayModal', { timeout: 10000 })
    .should('be.visible')
    .within(() => {
      cy.get('#modalHolidayName').should('contain', 'Christmas Break');
      cy.get('#modalHolidayDate').should('contain', '12/24/2030 - 12/26/2030');
      cy.get('#modalScheduleTypesList').should('be.visible');
      cy.get('#confirmHolidayCheckbox').should('be.visible').click({ force: true });
      cy.get('#btnConfirmSave').should('be.enabled').click({ force: true });
    });

  // Handle the success popup
  cy.get('.swal2-popup', { timeout: 10000 })
    .should('be.visible')
    .and('contain', 'Success')
    .and('contain', 'Holiday saved successfully');
  cy.get('.swal2-confirm').click({ force: true });

  // Wait for form to be hidden and Add Holiday button to be visible again
  cy.get('#frmAddHoliday').should('not.be.visible');
  cy.get('#btnAddHoliday').should('be.visible');
  cy.wait(1000);

  // Add New Year Break
  cy.get('#btnAddHoliday').should('be.visible').click({ force: true });
  cy.wait(500);

  // Fill out New Year Break form
  cy.get('#holidayDateType').should('be.visible').select('range', { force: true });
  cy.wait(500);
  admin.holidayName.should('be.visible').type('New Year Break');
  cy.get('#holidayStartDate').should('be.visible').type('12/31/2030', { force: true });
  cy.get('td.active.day').click();
  cy.get('#holidayEndDate').should('be.visible').type('01/02/2031', { force: true });
  cy.get('td.active.day').click();
  admin.saveAddHoliday.click();

  // Handle the confirmation modal
  cy.get('#confirmHolidayModal', { timeout: 10000 })
    .should('be.visible')
    .within(() => {
      cy.get('#modalHolidayName').should('contain', 'New Year Break');
      cy.get('#modalHolidayDate').should('contain', '12/31/2030 - 01/02/2031');
      cy.get('#modalScheduleTypesList').should('be.visible');
      cy.get('#confirmHolidayCheckbox').should('be.visible').click({ force: true });
      cy.get('#btnConfirmSave').should('be.enabled').click({ force: true });
    });

  // Handle the success popup
  cy.get('.swal2-popup', { timeout: 10000 })
    .should('be.visible')
    .and('contain', 'Success')
    .and('contain', 'Holiday saved successfully');
  cy.get('.swal2-confirm').click({ force: true });

  // Wait for form to be hidden and Add Holiday button to be visible again
  cy.get('#frmAddHoliday').should('not.be.visible');
  cy.get('#btnAddHoliday').should('be.visible');
  cy.wait(1000); // Additional wait to ensure everything is settled

  // Verify all holidays are displayed in the table
  admin.holidayTable.should('have.length', 6); // 4 individual + 2 range holidays

  // Test editing a holiday
  admin.editHoliday.eq(3).scrollIntoView();
  admin.editHoliday.eq(3).click({ force: true });
  admin.holidayName.clear().type('Thanksgiving Break', { force: true });
  admin.saveAddHoliday.click({ force: true });

  cy.get('.swal2-popup', { timeout: 10000 })
    .should('be.visible')
    .and('contain', 'Editing Holiday')
    .within(() => {
      cy.get('[data-cy="holiday-confirmation-checkbox"]').should('be.visible').click({ force: true });
      cy.get('[data-cy="holiday-edit-save-btn"]').should('be.enabled').click({ force: true });
    });

  // Handle the success popup
  cy.get('.swal2-popup', { timeout: 10000 })
    .should('be.visible')
    .and('contain', 'Success')
    .and('contain', 'Holiday saved successfully');
  cy.get('.swal2-confirm').click({ force: true });

  // Wait for form to be hidden and Add Holiday button to be visible again
  cy.get('#frmAddHoliday').should('not.be.visible');
  cy.get('#btnAddHoliday').should('be.visible');
  cy.wait(1000); // Additional wait to ensure everything is settled

  // Test deleting a holiday
  admin.deleteHoliday.eq(1).click();
  admin.holidayDeleteConfirmationCheckbox.click();
  admin.holidayDeleteSaveBtn.click();
};

function addIndividualHolidays() {
  HOLIDAYS.forEach((holiday) => {
    // Make sure form is visible
    cy.get('#btnAddHoliday').should('be.visible').click({ force: true });
    cy.wait(500); // Wait for form to be visible

    admin.holidayName.type(holiday.name);
    admin.holidayDate.type(holiday.holidayDate, { force: true });
    cy.get('td.active.day').click();
    admin.saveAddHoliday.click();

    // Handle the confirmation modal
    cy.get('#confirmHolidayModal', { timeout: 10000 })
      .should('be.visible')
      .within(() => {
        cy.get('#modalHolidayName').should('contain', holiday.name);
        cy.get('#modalHolidayDate').should('be.visible');
        cy.get('#modalScheduleTypesList').should('be.visible');
        cy.get('#confirmHolidayCheckbox').should('be.visible').click({ force: true });
        cy.get('#btnConfirmSave').should('be.enabled').click({ force: true });
      });

    // Handle the success popup
    cy.get('.swal2-popup', { timeout: 10000 })
      .should('be.visible')
      .and('contain', 'Success')
      .and('contain', 'Holiday saved successfully');
    cy.get('.swal2-confirm').click({ force: true });

    // Wait for form to be hidden and Add Holiday button to be visible again
    cy.get('#frmAddHoliday').should('not.be.visible');
    cy.get('#btnAddHoliday').should('be.visible');
    cy.wait(1000); // Additional wait to ensure everything is settled
  });
}

function confirm() {
  cy.contains('button.swal2-confirm', 'OK').click({ force: true });
  cy.wait(500);
  cy.contains('OK', { timeout: 2000 }).click({ force: true });
}
