import {
  BASE_URLS,
  CHAT_SUPPORT_DATA,
  DESIGNATIONS,
  CHECK_IN_SETTINGS,
  PEOPLE_DIRECTORY
} from '../../../support/constants';
import Admin from 'cypress/pages/admin';
import header from 'cypress/pages/header';
import navigation from 'cypress/pages/navigation';
import people from 'cypress/pages/people';
import admin from 'cypress/pages/admin';
import checkinCheckout from 'cypress/pages/checkin-checkout';
import groups from 'cypress/pages/groups';
import roster from 'cypress/pages/roster';

context('Admin Configuration Part 1', () => {
  beforeEach(() => {
    cy.setDefaultDatabase();
    cy.login(BASE_URLS.ADMIN);
  });

  it('Checks chat support configuration', () => {
    checkChatSupportConfig();
  });

  it('Checks check in/out config', () => {
    checkPinCodeCheck();
    checkStaffCheckInOutConf();
    checkStaffPinCheck();
    checkKioskMasterPin();
    checkKioskMasterPinStaff();
    checkDisableMasterPinCode();
    checkPhonePin();
    checkQrCodeCheckIn();
    checkMultipleCheck();
  });

  it('Checks designations, auto checkout', () => {
    checkDesignations();
    checkAutoCheckOut();
    checkExpressDriveUp();
  });
});

const checkChatSupportConfig = () => {
  header.helpBadgeBtn.click();
  header.chatWithTheSchool.should('exist').and('include.text', CHAT_SUPPORT_DATA.OLD_URL);
  Admin.configureChatSupport.scrollIntoView().click();

  Admin.chatUrlText.type(BASE_URLS.GROUPS);
  Admin.menuVerbiage.clear();
  Admin.menuVerbiage.type(CHAT_SUPPORT_DATA.NEW_URL);
  Admin.saveChatSupportBtn.click();
  cy.containsOkAndClick();

  header.helpBadgeBtn.click();
  header.chatWithTheSchool.should('exist').and('include.text', CHAT_SUPPORT_DATA.NEW_URL);
  header.chatWithTheSchool.invoke('removeAttr', 'target').click();
  cy.url().should('include', BASE_URLS.GROUPS);

  navigation.adminNav.click();
  Admin.configureChatSupport.scrollIntoView().click();
  Admin.chatSupportEnabled.click({ force: true });
  Admin.saveChatSupportBtn.click();
  cy.containsOkAndClick();

  header.helpBadgeBtn.click();
  header.chatWithTheSchool.should('not.exist');
};

const checkPinCodeCheck = () => {
  admin.configureCheckInOut.click();
  admin.pinCodeCheckIn.check({ force: true });
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.peopleFilter.click();
  people.selectPeopleCheckbox.check({ force: true });
  people.selectStaffCheckbox.check({ force: true });
  people.peopleFilter.click();
  people.pinCheck.click();
  people.pinCode.type(PEOPLE_DIRECTORY.PEOPLE_PIN);
  people.pinSubmit.click();
  cy.wait(500);
  cy.get('body').contains(PEOPLE_DIRECTORY.CHECK_IN_OUT);
  cy.containsOkAndClick();
  people.pinCode.type(PEOPLE_DIRECTORY.PEOPLE_PIN);
  people.pinSubmit.click();
  cy.containsOkAndClick();
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.checkInButton.first().then(($checkIn) => {
    expect($checkIn.text().trim()).eq(PEOPLE_DIRECTORY.CHECKED_IN);
  });
};

const checkStaffCheckInOutConf = () => {
  navigation.adminNav.click();
  admin.configureCheckInOut.click();
  admin.staffRequiredPinCode.check({ force: true });
  cy.wait(500);
  cy.performLogout();
};

const checkStaffPinCheck = () => {
  cy.login(BASE_URLS.PEOPLE_DIRECTORY, 'Staff');
  people.checkInButton.eq(0).click();
  checkinCheckout.checkoutModalSave.click();
  cy.get('#checkout-alert').should('be.visible');
  checkinCheckout.checkAlertText.should('contain.text', CHECK_IN_SETTINGS.ALERT_MSG);
  cy.visit(BASE_URLS.PEOPLE_DIRECTORY);
  navigation.loadingSpinner.should('not.exist');
  cy.performLogout();
};

const checkKioskMasterPin = () => {
  cy.login(BASE_URLS.ADMIN);
  admin.configureCheckInOut.click();
  admin.changeMasterPinCode.click();
  cy.get('.swal2-html-container').should('be.visible');
  cy.get('.swal2-input').type(CHECK_IN_SETTINGS.MASTER_PIN_CODE);
  cy.containsOkAndClick();
  cy.performLogout();
};

const checkKioskMasterPinStaff = () => {
  cy.login(BASE_URLS.DEFAULT, 'Staff');
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.pinCheck.click();
  people.pinCode.type(CHECK_IN_SETTINGS.MASTER_PIN_CODE);
  people.pinSubmit.click();
  people.pinCodeModal.should('be.visible');
  people.pinCode.type(PEOPLE_DIRECTORY.PEOPLE_PIN);
  people.pinSubmit.click();
  validateSuccessfulCheck();
  cy.visit(BASE_URLS.GROUPS);
  navigation.loadingSpinner.should('not.exist');
  cy.performLogout();
};

const checkDisableMasterPinCode = () => {
  cy.login(BASE_URLS.ADMIN);
  admin.configureCheckInOut.click();
  admin.changeMasterPinCode.click();
  cy.get('.swal2-html-container').should('be.visible');
  cy.containsOkAndClick();
};

const checkPhonePin = () => {
  admin.phonePinCheckIn.check({ force: true });
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.pinCheck.click();
  people.phonePinForm.should('be.visible');
};

const checkQrCodeCheckIn = () => {
  navigation.adminNav.click();
  admin.configureCheckInOut.click();
  admin.phonePinCheckIn.uncheck({ force: true });
  admin.qrCodeCheckIn.check({ force: true });
  cy.wait(500);
  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.pinCheck.click();
  people.qrCode.should('be.visible');
};

const checkMultipleCheck = () => {
  navigation.adminNav.click();
  admin.configureCheckInOut.click();
  admin.multipleCheckIn.check({ force: true });
  cy.wait(500);
  navigation.manageNav.click({ force: true });
  navigation.groupsNav.click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  groups.groupRosterName.contains('KinderGarden').parents('.card').find('[data-cy="group-roster-btn"]').click();
  roster.selectMultipleBtn.click();
  roster.multipleCheckIn.should('be.visible');

  people.peopleCard.each(($card, index) => {
    if (index < 2) {
      cy.wrap($card).click().should('have.class', 'mp-selected-card');
    }
  });
  roster.multipleCheckIn.click();
  cy.containsOkAndClick();
};

function validateSuccessfulCheck() {
  cy.get('.swal2-popup.swal2-modal.swal2-icon-success').should('be.visible');
  cy.get('.swal2-html-container').should('contain.text', 'Check In/Out Successful:');
  cy.get('.swal2-html-container').should('contain.text', 'Checked out MattStaff C');
}

const checkDesignations = () => {
  Admin.configureDesignations.click();
  Admin.addDesignations.click();
  Admin.designationName.type('Test');
  Admin.saveDesignation.click();
  cy.wait(500);
  Admin.designationItem.should('have.length', 1);

  Admin.addDesignations.click();
  Admin.designationName.type('Registration');
  Admin.saveDesignation.click();
  cy.wait(500);
  Admin.designationItem.each((row, index) => {
    const expected = DESIGNATIONS[index + 1];
    cy.wrap(row).within(() => {
      Admin.designation.should('contain.text', expected.text);
      Admin.registrationUrl.should('contain.text', expected.url);
    });
  });

  navigation.manageNav.click({ force: true });
  navigation.peopleNav.should('be.visible').click({ force: true });
  navigation.loadingSpinner.should('not.exist');
  people.newRegistrationLink.click();
  people.designationNameUrl.each((link, index) => {
    const expected = DESIGNATIONS[index];
    cy.wrap(link).should('contain.text', expected.text);
    cy.wrap(link)
      .invoke('attr', 'href')
      .then((href) => {
        const expectedUrlWithoutPerson = expected.url.split('&person=')[0];
        const actualUrlWithoutPerson = href.split('&person=')[0];
        expect(actualUrlWithoutPerson).to.equal(expectedUrlWithoutPerson);
      });
  });
  people.designationNameUrl.should('have.length', 3);
  cy.get('.btn[data-dismiss="modal"]').click({ force: true });
};

const checkAutoCheckOut = () => {
  cy.visit(BASE_URLS.ADMIN);
  navigation.loadingSpinner.should('not.exist');
  admin.configureAutoCheckout.click();
  admin.enabledAutoCheckout.check({ force: true }).should('be.checked');
  admin.autoCheckoutTime.clear().type('12:00');
};

const checkExpressDriveUp = () => {
  admin.configureExpressDriveUp.click();
  admin.arrivedText.clear().type('Will be there');
  admin.saveExpressDriveUp.click();
  cy.containsOkAndClick();
};
