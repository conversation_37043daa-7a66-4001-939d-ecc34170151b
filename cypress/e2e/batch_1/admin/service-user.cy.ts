import Admin from 'cypress/pages/admin';
import { BASE_URLS, SERVICE_ACCOUNT } from 'cypress/support/constants';

let password;
context('Service user account validation ', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.SERVICE_ACCOUNT);
  });

  it('Check service account creation and existing user ', () => {
    createServiceAccount();
    passwordValidation();
    editServiceAccount();
    deleteServiceAccount();
  });

  const createServiceAccount = () => {
    Admin.serviceAccountManagerBtn.click();
    Admin.addServiceAccountBtn.click();
    Admin.addServiceAccountEmail.type(SERVICE_ACCOUNT.EMAIL1);
    password = Admin.addServiceAccountPassword.invoke('text');
    Admin.saveServiceAccountBtn.click();
  };

  const editServiceAccount = () => {
    Admin.editServiceAccount.click();
    Admin.changeServiceAccountPasswordBtn.click();
    Admin.addServiceAccountEmail.should('be.disabled');
    Admin.addServiceAccountPassword.should('be.disabled');
    Admin.saveServiceAccountBtn.click();
  };

  const passwordValidation = () => {
    Admin.editServiceAccount.click();
    Admin.addServiceAccountPassword.should('be.disabled');
    Admin.changeServiceAccountPasswordBtn.click();
    const password1 = Admin.addServiceAccountPassword.invoke('text');
    expect(password).to.not.eq(password1);
  };

  const deleteServiceAccount = () => {
    Admin.deleteServiceAccount.click();
    cy.get('.swal2-confirm').click({ force: true });
    cy.containsOkAndClick();
    Admin.serviceAccountTable.find('tr').should('have.length.lessThan', 2);
  };
});
