import { LOGIN_DATA } from 'cypress/support/constants';

context('authorization', () => {
  it('visit login page', () => {
    cy.visit('/login/legacy');

    cy.get('[data-cy=email-login-input').click().type(LOGIN_DATA.EMAIL);
    cy.get('[data-cy=password-login-input').click().type(LOGIN_DATA.PASSWORD);
    cy.get('[data-cy=sign-in-btn').click();
    cy.url().should('match', /my-site/, { timeout: 5000 });
  });

  it('should verify URL cleanup after login', () => {
    cy.visit('/login/legacy');
    cy.get('[data-cy=email-login-input').click().type(LOGIN_DATA.EMAIL);
    cy.get('[data-cy=password-login-input').click().type(LOGIN_DATA.PASSWORD);
    cy.get('[data-cy=sign-in-btn').click();

    cy.url().should('match', /my-site$/, { timeout: 5000 });
    cy.url().should('not.include', 'code=');
    cy.url().should('not.include', 'state=');
  });
});
