import header from 'cypress/pages/header';
import { BASE_URLS } from 'cypress/support/constants';

context('Logout Functionality', () => {
  beforeEach(() => {
    cy.login(BASE_URLS.GROUPS);
    cy.wait(5000);
  });

  it('should successfully log out the user', () => {
    performLogout();
  });
});

const performLogout = () => {
  header.quickUserToggle.click();
  header.signOutBtn.click();
  cy.wait(2000);

  cy.visit(BASE_URLS.LOGIN);
  cy.reload();
  verifyLoggedOut();
};

const verifyLoggedOut = () => {
  cy.get('[data-cy=email-login-input').should('be.visible');
  header.currentUserName.should('not.exist');
  cy.visit(BASE_URLS.DEFAULT, { failOnStatusCode: false });
  header.currentUserName.should('not.exist');
};
